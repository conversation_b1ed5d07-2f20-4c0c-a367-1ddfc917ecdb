#!/usr/bin/env python3
"""
Qwen Coder Agent - Highly Intelligent Coding Assistant
Inspired by <PERSON>urs<PERSON>, <PERSON><PERSON>, C<PERSON>, and Kilo

Features:
- Inline code editing with diff preview
- Terminal command execution
- Repository-wide code understanding
- Interactive chat interface
- File management and creation
- Git integration
- Multi-language support
- Context-aware suggestions
"""

import os
import sys
import json
import subprocess
import requests
import difflib
import argparse
import readline
import glob
import re
import shutil
import tempfile
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from datetime import datetime

# Color codes for terminal output
class Colors:
    HEADER = '\033[95m'
    OKBLUE = '\033[94m'
    OKCYAN = '\033[96m'
    OKGREEN = '\033[92m'
    WARNING = '\033[93m'
    FAIL = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'

@dataclass
class FileChange:
    file_path: str
    original_content: str
    new_content: str
    change_type: str  # 'create', 'modify', 'delete'
    line_start: Optional[int] = None
    line_end: Optional[int] = None

@dataclass
class CommandExecution:
    command: str
    output: str
    exit_code: int
    timestamp: datetime

class QwenCoderAgent:
    def __init__(self, config_path: str = "~/.qwen-coder-config.json"):
        self.config_path = os.path.expanduser(config_path)
        self.config = self.load_config()
        self.session_history = []
        self.pending_changes = []
        self.executed_commands = []
        self.current_directory = os.getcwd()
        self.context_files = []
        self.active_files = set()
        
        # Initialize readline for better CLI experience
        readline.set_completer(self.path_completer)
        readline.parse_and_bind("tab: complete")
        
        self.print_banner()
        self.load_project_context()

    def print_banner(self):
        """Print startup banner"""
        banner = f"""
{Colors.HEADER}╔══════════════════════════════════════════════════════════════╗
║                🤖 Qwen Coder Agent v2.0                     ║
║           Intelligent Coding Assistant & Agent              ║
╚══════════════════════════════════════════════════════════════╝{Colors.ENDC}

{Colors.OKCYAN}📁 Working directory: {self.current_directory}{Colors.ENDC}
{Colors.OKGREEN}🚀 Ready to assist with coding, debugging, and development!{Colors.ENDC}

{Colors.BOLD}Quick Commands:{Colors.ENDC}
  • {Colors.OKCYAN}help{Colors.ENDC} - Show all commands
  • {Colors.OKCYAN}edit <file>{Colors.ENDC} - Edit file with AI assistance
  • {Colors.OKCYAN}run <command>{Colors.ENDC} - Execute terminal command
  • {Colors.OKCYAN}analyze{Colors.ENDC} - Analyze current project
  • {Colors.OKCYAN}chat{Colors.ENDC} - Start interactive chat mode

{Colors.WARNING}Type your request or command below:{Colors.ENDC}
"""
        print(banner)

    def load_config(self) -> Dict:
        """Load configuration from file or create default"""
        default_config = {
            "api_key": os.getenv("HF_TOKEN", ""),
            "base_url": "https://api-inference.huggingface.co/models/Qwen/Qwen2.5-Coder-32B-Instruct",
            "model": "Qwen/Qwen2.5-Coder-32B-Instruct",
            "max_tokens": 4096,
            "temperature": 0.7,
            "auto_apply_changes": False,
            "git_integration": True,
            "backup_files": True,
            "context_lines": 50,
            "max_file_size": 100000,  # 100KB
            "excluded_dirs": [".git", "node_modules", "__pycache__", ".vscode", "dist", "build"],
            "included_extensions": [".py", ".js", ".ts", ".jsx", ".tsx", ".java", ".cpp", ".c", ".go", ".rs", ".php", ".rb", ".swift", ".kt", ".scala", ".cs", ".html", ".css", ".scss", ".sass", ".vue", ".svelte", ".md", ".json", ".yaml", ".yml", ".toml", ".xml", ".sql"]
        }
        
        if os.path.exists(self.config_path):
            try:
                with open(self.config_path, 'r') as f:
                    config = json.load(f)
                    return {**default_config, **config}
            except Exception as e:
                print(f"{Colors.WARNING}Warning: Could not load config: {e}{Colors.ENDC}")
        
        return default_config

    def save_config(self):
        """Save current configuration"""
        try:
            os.makedirs(os.path.dirname(self.config_path), exist_ok=True)
            with open(self.config_path, 'w') as f:
                json.dump(self.config, f, indent=2)
            print(f"{Colors.OKGREEN}✓ Configuration saved{Colors.ENDC}")
        except Exception as e:
            print(f"{Colors.FAIL}Error saving config: {e}{Colors.ENDC}")

    def path_completer(self, text, state):
        """Tab completion for file paths"""
        try:
            matches = glob.glob(text + '*')
            if state < len(matches):
                return matches[state]
        except:
            pass
        return None

    def load_project_context(self):
        """Load and analyze project context"""
        print(f"{Colors.OKCYAN}🔍 Analyzing project structure...{Colors.ENDC}")
        
        # Detect project type
        project_type = self.detect_project_type()
        if project_type:
            print(f"{Colors.OKGREEN}📦 Detected {project_type} project{Colors.ENDC}")
        
        # Load key files into context
        self.load_key_files()
        
        # Check git status
        self.check_git_status()

    def detect_project_type(self) -> Optional[str]:
        """Detect the type of project"""
        if os.path.exists("package.json"):
            return "Node.js/JavaScript"
        elif os.path.exists("requirements.txt") or os.path.exists("pyproject.toml"):
            return "Python"
        elif os.path.exists("Cargo.toml"):
            return "Rust"
        elif os.path.exists("go.mod"):
            return "Go"
        elif os.path.exists("pom.xml") or os.path.exists("build.gradle"):
            return "Java"
        elif os.path.exists("composer.json"):
            return "PHP"
        elif os.path.exists("Gemfile"):
            return "Ruby"
        elif os.path.exists("*.csproj"):
            return "C#/.NET"
        return None

    def load_key_files(self):
        """Load important project files into context"""
        key_files = [
            "README.md", "package.json", "requirements.txt", "Cargo.toml", 
            "go.mod", "pom.xml", "composer.json", "Gemfile", "tsconfig.json",
            ".env.example", "docker-compose.yml", "Dockerfile"
        ]
        
        for file in key_files:
            if os.path.exists(file) and os.path.getsize(file) < self.config["max_file_size"]:
                self.context_files.append(file)

    def check_git_status(self):
        """Check git repository status"""
        try:
            # Check if we're in a git repo
            subprocess.check_output(['git', 'rev-parse', '--git-dir'], stderr=subprocess.DEVNULL)
            
            # Get current branch
            branch = subprocess.check_output(['git', 'branch', '--show-current'], 
                                           stderr=subprocess.DEVNULL).decode().strip()
            
            # Get status
            status = subprocess.check_output(['git', 'status', '--porcelain'], 
                                           stderr=subprocess.DEVNULL).decode().strip()
            
            print(f"{Colors.OKBLUE}🌿 Git branch: {branch}{Colors.ENDC}")
            if status:
                print(f"{Colors.WARNING}📝 Uncommitted changes detected{Colors.ENDC}")
            else:
                print(f"{Colors.OKGREEN}✓ Working directory clean{Colors.ENDC}")
                
        except subprocess.CalledProcessError:
            print(f"{Colors.WARNING}📁 Not a git repository{Colors.ENDC}")

    def get_repository_context(self, include_file_contents: bool = True) -> str:
        """Get comprehensive repository context"""
        context = []
        
        # Project info
        project_type = self.detect_project_type()
        if project_type:
            context.append(f"Project Type: {project_type}")
        
        # Git info
        try:
            branch = subprocess.check_output(['git', 'branch', '--show-current'], 
                                           stderr=subprocess.DEVNULL).decode().strip()
            context.append(f"Git Branch: {branch}")
            
            # Recent commits
            commits = subprocess.check_output(['git', 'log', '--oneline', '-5'], 
                                            stderr=subprocess.DEVNULL).decode().strip()
            context.append(f"Recent Commits:\n{commits}")
        except:
            pass
        
        # Directory structure
        context.append("\nProject Structure:")
        context.append(self.get_directory_tree())
        
        # Key file contents
        if include_file_contents:
            context.append("\nKey Files:")
            for file_path in self.context_files:
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        if len(content) > 1000:
                            content = content[:1000] + "... (truncated)"
                        context.append(f"\n--- {file_path} ---\n{content}")
                except Exception as e:
                    context.append(f"\n--- {file_path} --- (Error reading: {e})")
        
        # Active files in session
        if self.active_files:
            context.append(f"\nActive Files in Session: {', '.join(self.active_files)}")
        
        return '\n'.join(context)

    def get_directory_tree(self, max_depth: int = 3) -> str:
        """Generate directory tree structure"""
        tree_lines = []
        
        def add_tree_line(path: Path, prefix: str = "", depth: int = 0):
            if depth > max_depth:
                return
            
            if path.name.startswith('.') or path.name in self.config["excluded_dirs"]:
                return
            
            tree_lines.append(f"{prefix}{path.name}")
            
            if path.is_dir():
                try:
                    children = sorted(path.iterdir(), key=lambda x: (x.is_file(), x.name.lower()))
                    for i, child in enumerate(children[:20]):  # Limit to 20 items per directory
                        is_last = i == len(children) - 1
                        child_prefix = prefix + ("└── " if is_last else "├── ")
                        next_prefix = prefix + ("    " if is_last else "│   ")
                        
                        if child.is_dir():
                            tree_lines.append(f"{child_prefix}{child.name}/")
                            if depth < max_depth:
                                add_tree_line(child, next_prefix, depth + 1)
                        else:
                            if any(child.name.endswith(ext) for ext in self.config["included_extensions"]):
                                tree_lines.append(f"{child_prefix}{child.name}")
                except PermissionError:
                    pass
        
        add_tree_line(Path('.'))
        return '\n'.join(tree_lines[:100])  # Limit total lines

    def call_qwen_api(self, prompt: str, system_prompt: str = None, include_context: bool = True) -> str:
        """Call Qwen API with enhanced context and error handling"""
        if not self.config["api_key"]:
            return "❌ Error: No API key configured. Set HF_TOKEN environment variable or run 'config' command."
        
        headers = {
            "Authorization": f"Bearer {self.config['api_key']}",
            "Content-Type": "application/json"
        }
        
        # Build comprehensive prompt
        full_prompt = self.build_comprehensive_prompt(prompt, system_prompt, include_context)
        
        payload = {
            "inputs": full_prompt,
            "parameters": {
                "max_new_tokens": self.config["max_tokens"],
                "temperature": self.config["temperature"],
                "return_full_text": False,
                "do_sample": True,
                "top_p": 0.9,
                "repetition_penalty": 1.1
            }
        }
        
        try:
            print(f"{Colors.OKCYAN}🤖 Thinking...{Colors.ENDC}")
            response = requests.post(self.config["base_url"], headers=headers, json=payload, timeout=60)
            response.raise_for_status()
            
            result = response.json()
            if isinstance(result, list) and len(result) > 0:
                generated_text = result[0].get("generated_text", "No response generated")
            else:
                generated_text = str(result)
            
            # Store in session history
            self.session_history.append({
                "user": prompt,
                "assistant": generated_text,
                "timestamp": datetime.now().isoformat()
            })
            
            return generated_text
                
        except requests.exceptions.RequestException as e:
            return f"❌ API Error: {e}"
        except Exception as e:
            return f"❌ Error: {e}"

    def build_comprehensive_prompt(self, prompt: str, system_prompt: str = None, include_context: bool = True) -> str:
        """Build a comprehensive prompt with all necessary context"""
        parts = []
        
        # System prompt
        if system_prompt:
            parts.append(f"System: {system_prompt}")
        else:
            parts.append("""System: You are Qwen Coder Agent, an expert AI coding assistant. You can:
- Write, edit, and debug code in any programming language
- Execute terminal commands and interpret results
- Analyze codebases and suggest improvements
- Create new files and modify existing ones
- Provide detailed explanations and documentation

When providing code changes:
1. Use proper code blocks with language specification
2. Include file paths when creating/modifying files
3. Provide clear explanations of changes
4. Consider the existing codebase context

Format file operations as:
```language
// File: path/to/file.ext
code content here
```""")
        
        # Repository context
        if include_context:
            repo_context = self.get_repository_context()
            parts.append(f"Repository Context:\n{repo_context}")
        
        # Recent conversation history
        if self.session_history:
            parts.append("Recent Conversation:")
            for entry in self.session_history[-2:]:  # Last 2 exchanges
                parts.append(f"User: {entry['user']}")
                assistant_text = entry['assistant']
                if len(assistant_text) > 300:
                    assistant_text = assistant_text[:300] + "..."
                parts.append(f"Assistant: {assistant_text}")
        
        # Recent command executions
        if self.executed_commands:
            parts.append("Recent Commands:")
            for cmd in self.executed_commands[-3:]:
                parts.append(f"$ {cmd.command}")
                if cmd.output:
                    output = cmd.output[:200] + "..." if len(cmd.output) > 200 else cmd.output
                    parts.append(f"Output: {output}")
        
        # Current request
        parts.append(f"Current Request: {prompt}")
        
        return "\n\n".join(parts)

    def parse_ai_response(self, response: str) -> Tuple[str, List[FileChange], List[str]]:
        """Parse AI response for code blocks, file operations, and commands"""
        file_changes = []
        commands = []
        explanation = response

        # Extract code blocks with file paths
        code_block_pattern = r'```(\w+)?\s*(?:\n(?:(?:File|Path):\s*([^\n]+)\n)?)?(.*?)```'
        code_blocks = re.findall(code_block_pattern, response, re.DOTALL)

        for language, file_path, code in code_blocks:
            if file_path and file_path.strip():
                file_path = file_path.strip()
                code = code.strip()

                # Determine change type
                if os.path.exists(file_path):
                    with open(file_path, 'r', encoding='utf-8') as f:
                        original_content = f.read()
                    change_type = 'modify'
                else:
                    original_content = ""
                    change_type = 'create'

                file_changes.append(FileChange(
                    file_path=file_path,
                    original_content=original_content,
                    new_content=code,
                    change_type=change_type
                ))

        # Extract terminal commands
        command_pattern = r'(?:```(?:bash|shell|cmd)\s*\n(.*?)\n```|`([^`]+)`|\$\s*([^\n]+))'
        command_matches = re.findall(command_pattern, response, re.DOTALL)

        for match in command_matches:
            command = match[0] or match[1] or match[2]
            if command and command.strip() and not command.startswith('#'):
                commands.append(command.strip())

        return explanation, file_changes, commands

    def show_diff(self, original: str, new: str, file_path: str):
        """Show colored diff between original and new content"""
        print(f"\n{Colors.HEADER}📝 Changes for {file_path}:{Colors.ENDC}")

        original_lines = original.splitlines(keepends=True)
        new_lines = new.splitlines(keepends=True)

        diff = difflib.unified_diff(
            original_lines, new_lines,
            fromfile=f"a/{file_path}",
            tofile=f"b/{file_path}",
            lineterm=""
        )

        for line in diff:
            if line.startswith('+++') or line.startswith('---'):
                print(f"{Colors.BOLD}{line.rstrip()}{Colors.ENDC}")
            elif line.startswith('@@'):
                print(f"{Colors.OKCYAN}{line.rstrip()}{Colors.ENDC}")
            elif line.startswith('+'):
                print(f"{Colors.OKGREEN}{line.rstrip()}{Colors.ENDC}")
            elif line.startswith('-'):
                print(f"{Colors.FAIL}{line.rstrip()}{Colors.ENDC}")
            else:
                print(line.rstrip())

    def apply_changes(self, changes: List[FileChange], auto_apply: bool = None):
        """Apply pending changes to files with user confirmation"""
        if not changes:
            print(f"{Colors.WARNING}No file changes to apply{Colors.ENDC}")
            return

        if auto_apply is None:
            auto_apply = self.config["auto_apply_changes"]

        print(f"\n{Colors.HEADER}📋 Pending file changes:{Colors.ENDC}")
        for i, change in enumerate(changes):
            status_icon = "📄" if change.change_type == "create" else "✏️" if change.change_type == "modify" else "🗑️"
            print(f"  {i+1}. {status_icon} {change.change_type.title()} {change.file_path}")

        if not auto_apply:
            print(f"\n{Colors.OKCYAN}Options:{Colors.ENDC}")
            print("  y/yes - Apply all changes")
            print("  n/no - Cancel all changes")
            print("  p/preview - Show detailed diff preview")
            print("  s/select - Select specific changes to apply")

            choice = input(f"\n{Colors.OKCYAN}Your choice: {Colors.ENDC}").lower().strip()

            if choice in ['p', 'preview']:
                for change in changes:
                    if change.change_type == 'modify':
                        self.show_diff(change.original_content, change.new_content, change.file_path)
                    elif change.change_type == 'create':
                        print(f"\n{Colors.HEADER}📄 New file: {change.file_path}{Colors.ENDC}")
                        print(f"{Colors.OKGREEN}{change.new_content}{Colors.ENDC}")
                return
            elif choice in ['s', 'select']:
                selected_changes = self.select_changes(changes)
                changes = selected_changes
            elif choice not in ['y', 'yes']:
                print(f"{Colors.WARNING}Changes cancelled{Colors.ENDC}")
                return

        # Backup files if enabled
        if self.config["backup_files"]:
            self.backup_files([c.file_path for c in changes if c.change_type != 'create'])

        # Apply changes
        success_count = 0
        for change in changes:
            try:
                if change.change_type in ['create', 'modify']:
                    os.makedirs(os.path.dirname(change.file_path) or '.', exist_ok=True)
                    with open(change.file_path, 'w', encoding='utf-8') as f:
                        f.write(change.new_content)
                    print(f"{Colors.OKGREEN}✓ {change.change_type.title()}d {change.file_path}{Colors.ENDC}")
                    self.active_files.add(change.file_path)
                elif change.change_type == 'delete':
                    os.remove(change.file_path)
                    print(f"{Colors.OKGREEN}✓ Deleted {change.file_path}{Colors.ENDC}")
                    self.active_files.discard(change.file_path)
                success_count += 1
            except Exception as e:
                print(f"{Colors.FAIL}✗ Error {change.change_type}ing {change.file_path}: {e}{Colors.ENDC}")

        print(f"\n{Colors.OKGREEN}✓ Applied {success_count}/{len(changes)} changes successfully{Colors.ENDC}")

    def select_changes(self, changes: List[FileChange]) -> List[FileChange]:
        """Allow user to select specific changes to apply"""
        selected = []

        for i, change in enumerate(changes):
            print(f"\n{Colors.HEADER}Change {i+1}: {change.change_type.title()} {change.file_path}{Colors.ENDC}")

            if change.change_type == 'modify':
                self.show_diff(change.original_content, change.new_content, change.file_path)
            elif change.change_type == 'create':
                print(f"{Colors.OKGREEN}New file content:\n{change.new_content[:500]}{'...' if len(change.new_content) > 500 else ''}{Colors.ENDC}")

            choice = input(f"{Colors.OKCYAN}Apply this change? (y/n): {Colors.ENDC}").lower().strip()
            if choice in ['y', 'yes']:
                selected.append(change)

        return selected

    def backup_files(self, file_paths: List[str]):
        """Create backups of files before modification"""
        backup_dir = ".qwen-backups"
        os.makedirs(backup_dir, exist_ok=True)

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        for file_path in file_paths:
            if os.path.exists(file_path):
                backup_name = f"{file_path.replace('/', '_').replace('\\', '_')}_{timestamp}.backup"
                backup_path = os.path.join(backup_dir, backup_name)
                try:
                    shutil.copy2(file_path, backup_path)
                    print(f"{Colors.OKBLUE}💾 Backed up {file_path} to {backup_path}{Colors.ENDC}")
                except Exception as e:
                    print(f"{Colors.WARNING}Warning: Could not backup {file_path}: {e}{Colors.ENDC}")

    def execute_command(self, command: str, confirm: bool = True) -> CommandExecution:
        """Execute terminal command with confirmation and output capture"""
        if confirm and not self.is_safe_command(command):
            print(f"{Colors.WARNING}⚠️  Potentially dangerous command detected:{Colors.ENDC}")
            print(f"{Colors.FAIL}$ {command}{Colors.ENDC}")

            choice = input(f"{Colors.OKCYAN}Execute anyway? (y/n): {Colors.ENDC}").lower().strip()
            if choice not in ['y', 'yes']:
                print(f"{Colors.WARNING}Command cancelled{Colors.ENDC}")
                return CommandExecution(command, "Command cancelled by user", -1, datetime.now())

        print(f"{Colors.OKCYAN}$ {command}{Colors.ENDC}")

        try:
            result = subprocess.run(
                command,
                shell=True,
                capture_output=True,
                text=True,
                timeout=60,
                cwd=self.current_directory
            )

            output = ""
            if result.stdout:
                output += result.stdout
            if result.stderr:
                output += f"\nSTDERR:\n{result.stderr}"

            # Color code the output based on exit code
            if result.returncode == 0:
                print(f"{Colors.OKGREEN}✓ Command executed successfully{Colors.ENDC}")
                if output.strip():
                    print(f"{Colors.OKBLUE}{output}{Colors.ENDC}")
            else:
                print(f"{Colors.FAIL}✗ Command failed with exit code {result.returncode}{Colors.ENDC}")
                if output.strip():
                    print(f"{Colors.FAIL}{output}{Colors.ENDC}")

            cmd_execution = CommandExecution(command, output, result.returncode, datetime.now())
            self.executed_commands.append(cmd_execution)

            # Keep only last 10 command executions
            if len(self.executed_commands) > 10:
                self.executed_commands = self.executed_commands[-10:]

            return cmd_execution

        except subprocess.TimeoutExpired:
            error = "Command timed out after 60 seconds"
            print(f"{Colors.FAIL}✗ {error}{Colors.ENDC}")
            return CommandExecution(command, error, -1, datetime.now())
        except Exception as e:
            error = f"Error executing command: {e}"
            print(f"{Colors.FAIL}✗ {error}{Colors.ENDC}")
            return CommandExecution(command, error, -1, datetime.now())

    def is_safe_command(self, command: str) -> bool:
        """Check if a command is potentially dangerous"""
        dangerous_patterns = [
            r'\brm\s+-rf\b',
            r'\bsudo\s+rm\b',
            r'\bdd\s+if=',
            r'\bmkfs\b',
            r'\bformat\b',
            r'\bdel\s+/[sq]\b',
            r'\brd\s+/s\b',
            r'>\s*/dev/',
            r'\bchmod\s+777\b',
            r'\bchown\s+.*root\b'
        ]

        for pattern in dangerous_patterns:
            if re.search(pattern, command, re.IGNORECASE):
                return False
        return True

    def read_file_content(self, file_path: str, line_start: int = None, line_end: int = None) -> str:
        """Read file content with optional line range"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                if line_start is None and line_end is None:
                    return f.read()

                lines = f.readlines()
                if line_start is not None:
                    line_start = max(1, line_start) - 1  # Convert to 0-based index
                if line_end is not None:
                    line_end = min(len(lines), line_end)

                return ''.join(lines[line_start:line_end])
        except Exception as e:
            return f"Error reading file: {e}"

    def handle_command(self, user_input: str) -> bool:
        """Handle user commands and return True if should continue"""
        user_input = user_input.strip()

        if not user_input:
            return True

        # Parse command
        parts = user_input.split()
        command = parts[0].lower()
        args = parts[1:] if len(parts) > 1 else []

        # Handle special commands
        if command in ['exit', 'quit', 'q']:
            print(f"{Colors.OKGREEN}👋 Goodbye! Happy coding!{Colors.ENDC}")
            return False

        elif command == 'help':
            self.show_help()

        elif command == 'config':
            self.handle_config_command(args)

        elif command == 'edit':
            if args:
                self.handle_edit_command(' '.join(args))
            else:
                print(f"{Colors.FAIL}Usage: edit <file_path> [description]{Colors.ENDC}")

        elif command == 'run':
            if args:
                self.execute_command(' '.join(args))
            else:
                print(f"{Colors.FAIL}Usage: run <command>{Colors.ENDC}")

        elif command == 'analyze':
            self.handle_analyze_command(args)

        elif command == 'chat':
            self.handle_chat_mode()

        elif command == 'status':
            self.show_status()

        elif command == 'history':
            self.show_history()

        elif command == 'clear':
            os.system('cls' if os.name == 'nt' else 'clear')
            self.print_banner()

        elif command == 'files':
            self.show_files()

        elif command == 'backup':
            self.handle_backup_command(args)

        elif command.startswith('/'):
            # Handle file operations
            self.handle_file_operation(user_input)

        else:
            # Treat as natural language request
            self.handle_ai_request(user_input)

        return True

    def show_help(self):
        """Show help information"""
        help_text = f"""
{Colors.HEADER}🤖 Qwen Coder Agent - Command Reference{Colors.ENDC}

{Colors.BOLD}File Operations:{Colors.ENDC}
  {Colors.OKCYAN}edit <file> [description]{Colors.ENDC}     - Edit file with AI assistance
  {Colors.OKCYAN}/create <file>{Colors.ENDC}               - Create new file
  {Colors.OKCYAN}/read <file> [start:end]{Colors.ENDC}     - Read file content
  {Colors.OKCYAN}/delete <file>{Colors.ENDC}              - Delete file

{Colors.BOLD}Code Operations:{Colors.ENDC}
  {Colors.OKCYAN}analyze [file/directory]{Colors.ENDC}     - Analyze code structure
  {Colors.OKCYAN}run <command>{Colors.ENDC}               - Execute terminal command
  {Colors.OKCYAN}chat{Colors.ENDC}                        - Enter interactive chat mode

{Colors.BOLD}Session Management:{Colors.ENDC}
  {Colors.OKCYAN}status{Colors.ENDC}                      - Show current session status
  {Colors.OKCYAN}history{Colors.ENDC}                     - Show conversation history
  {Colors.OKCYAN}files{Colors.ENDC}                       - Show active files
  {Colors.OKCYAN}clear{Colors.ENDC}                       - Clear screen

{Colors.BOLD}Configuration:{Colors.ENDC}
  {Colors.OKCYAN}config{Colors.ENDC}                      - Show current configuration
  {Colors.OKCYAN}config set <key> <value>{Colors.ENDC}    - Set configuration value
  {Colors.OKCYAN}backup list{Colors.ENDC}                 - List available backups
  {Colors.OKCYAN}backup restore <file>{Colors.ENDC}       - Restore from backup

{Colors.BOLD}General:{Colors.ENDC}
  {Colors.OKCYAN}help{Colors.ENDC}                        - Show this help
  {Colors.OKCYAN}exit/quit/q{Colors.ENDC}                 - Exit the agent

{Colors.BOLD}Natural Language:{Colors.ENDC}
  Just type your request in natural language, e.g.:
  • "Create a Python function to sort a list"
  • "Fix the bug in my authentication code"
  • "Add error handling to the API endpoints"
  • "Refactor this component to use hooks"
"""
        print(help_text)

    def handle_config_command(self, args: List[str]):
        """Handle configuration commands"""
        if not args:
            print(f"\n{Colors.HEADER}Current Configuration:{Colors.ENDC}")
            for key, value in self.config.items():
                if key == "api_key":
                    value = "***" + value[-4:] if value else "Not set"
                print(f"  {Colors.OKCYAN}{key}{Colors.ENDC}: {value}")
            return

        if args[0] == 'set' and len(args) >= 3:
            key = args[1]
            value = ' '.join(args[2:])

            # Type conversion for known config keys
            if key in ['max_tokens', 'context_lines', 'max_file_size']:
                try:
                    value = int(value)
                except ValueError:
                    print(f"{Colors.FAIL}Error: {key} must be a number{Colors.ENDC}")
                    return
            elif key in ['temperature']:
                try:
                    value = float(value)
                except ValueError:
                    print(f"{Colors.FAIL}Error: {key} must be a number{Colors.ENDC}")
                    return
            elif key in ['auto_apply_changes', 'git_integration', 'backup_files']:
                value = value.lower() in ['true', '1', 'yes', 'on']

            self.config[key] = value
            self.save_config()
            print(f"{Colors.OKGREEN}✓ Set {key} = {value}{Colors.ENDC}")
        else:
            print(f"{Colors.FAIL}Usage: config set <key> <value>{Colors.ENDC}")

    def handle_edit_command(self, file_and_description: str):
        """Handle file editing with AI assistance"""
        parts = file_and_description.split(' ', 1)
        file_path = parts[0]
        description = parts[1] if len(parts) > 1 else "Edit this file"

        # Read current file content if it exists
        if os.path.exists(file_path):
            current_content = self.read_file_content(file_path)
            prompt = f"""Edit the file '{file_path}' based on this request: {description}

Current file content:
```
{current_content}
```

Please provide the complete updated file content."""
        else:
            prompt = f"Create a new file '{file_path}' based on this request: {description}"

        self.active_files.add(file_path)
        self.handle_ai_request(prompt)

    def handle_analyze_command(self, args: List[str]):
        """Handle code analysis commands"""
        target = args[0] if args else "."

        if os.path.isfile(target):
            # Analyze specific file
            content = self.read_file_content(target)
            prompt = f"""Analyze this code file '{target}' and provide:
1. Code quality assessment
2. Potential issues or bugs
3. Improvement suggestions
4. Documentation recommendations

File content:
```
{content}
```"""
        else:
            # Analyze directory/project
            prompt = f"""Analyze this project structure and provide:
1. Architecture overview
2. Code organization assessment
3. Potential improvements
4. Best practices recommendations
5. Security considerations

Focus on the directory: {target}"""

        self.handle_ai_request(prompt)

    def handle_chat_mode(self):
        """Enter interactive chat mode"""
        print(f"\n{Colors.HEADER}💬 Entering Chat Mode{Colors.ENDC}")
        print(f"{Colors.OKCYAN}Type 'exit' to return to command mode{Colors.ENDC}\n")

        while True:
            try:
                user_input = input(f"{Colors.OKGREEN}You: {Colors.ENDC}").strip()

                if user_input.lower() in ['exit', 'quit', 'back']:
                    print(f"{Colors.OKCYAN}Exiting chat mode{Colors.ENDC}")
                    break

                if user_input:
                    self.handle_ai_request(user_input)

            except KeyboardInterrupt:
                print(f"\n{Colors.OKCYAN}Exiting chat mode{Colors.ENDC}")
                break

    def handle_ai_request(self, prompt: str):
        """Handle AI request and process response"""
        response = self.call_qwen_api(prompt)

        if response.startswith("❌"):
            print(f"\n{Colors.FAIL}{response}{Colors.ENDC}")
            return

        # Parse response for actions
        explanation, file_changes, commands = self.parse_ai_response(response)

        # Show AI response
        print(f"\n{Colors.HEADER}🤖 Qwen Coder Agent:{Colors.ENDC}")
        print(f"{Colors.OKBLUE}{explanation}{Colors.ENDC}")

        # Handle file changes
        if file_changes:
            self.pending_changes.extend(file_changes)
            self.apply_changes(file_changes)

        # Handle commands
        if commands:
            print(f"\n{Colors.HEADER}🔧 Suggested commands:{Colors.ENDC}")
            for i, cmd in enumerate(commands):
                print(f"  {i+1}. {Colors.OKCYAN}{cmd}{Colors.ENDC}")

            choice = input(f"\n{Colors.OKCYAN}Execute commands? (y/n/select): {Colors.ENDC}").lower().strip()

            if choice in ['y', 'yes']:
                for cmd in commands:
                    self.execute_command(cmd, confirm=False)
            elif choice in ['s', 'select']:
                for i, cmd in enumerate(commands):
                    execute = input(f"{Colors.OKCYAN}Execute '{cmd}'? (y/n): {Colors.ENDC}").lower().strip()
                    if execute in ['y', 'yes']:
                        self.execute_command(cmd, confirm=False)

    def handle_file_operation(self, operation: str):
        """Handle file operations like /create, /read, /delete"""
        parts = operation[1:].split(' ', 1)  # Remove leading '/'
        op = parts[0].lower()
        args = parts[1] if len(parts) > 1 else ""

        if op == 'create':
            if args:
                file_path = args
                content = input(f"{Colors.OKCYAN}Enter content for {file_path} (Ctrl+D to finish):\n{Colors.ENDC}")
                try:
                    os.makedirs(os.path.dirname(file_path) or '.', exist_ok=True)
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(content)
                    print(f"{Colors.OKGREEN}✓ Created {file_path}{Colors.ENDC}")
                    self.active_files.add(file_path)
                except Exception as e:
                    print(f"{Colors.FAIL}✗ Error creating {file_path}: {e}{Colors.ENDC}")
            else:
                print(f"{Colors.FAIL}Usage: /create <file_path>{Colors.ENDC}")

        elif op == 'read':
            if args:
                file_path = args
                line_range = None

                # Check for line range (e.g., file.py:10:20)
                if ':' in file_path:
                    parts = file_path.split(':')
                    file_path = parts[0]
                    try:
                        start = int(parts[1]) if len(parts) > 1 and parts[1] else None
                        end = int(parts[2]) if len(parts) > 2 and parts[2] else None
                        line_range = (start, end)
                    except ValueError:
                        print(f"{Colors.WARNING}Invalid line range format{Colors.ENDC}")

                content = self.read_file_content(file_path,
                                               line_range[0] if line_range else None,
                                               line_range[1] if line_range else None)

                print(f"\n{Colors.HEADER}📄 {file_path}{Colors.ENDC}")
                if line_range:
                    print(f"{Colors.OKCYAN}Lines {line_range[0] or 1}-{line_range[1] or 'end'}{Colors.ENDC}")
                print(f"{Colors.OKBLUE}{content}{Colors.ENDC}")

                self.active_files.add(file_path)
            else:
                print(f"{Colors.FAIL}Usage: /read <file_path> [start:end]{Colors.ENDC}")

        elif op == 'delete':
            if args:
                file_path = args
                if os.path.exists(file_path):
                    confirm = input(f"{Colors.WARNING}Delete {file_path}? (y/n): {Colors.ENDC}").lower().strip()
                    if confirm in ['y', 'yes']:
                        try:
                            os.remove(file_path)
                            print(f"{Colors.OKGREEN}✓ Deleted {file_path}{Colors.ENDC}")
                            self.active_files.discard(file_path)
                        except Exception as e:
                            print(f"{Colors.FAIL}✗ Error deleting {file_path}: {e}{Colors.ENDC}")
                else:
                    print(f"{Colors.FAIL}File {file_path} does not exist{Colors.ENDC}")
            else:
                print(f"{Colors.FAIL}Usage: /delete <file_path>{Colors.ENDC}")

        else:
            print(f"{Colors.FAIL}Unknown file operation: {op}{Colors.ENDC}")

    def show_status(self):
        """Show current session status"""
        print(f"\n{Colors.HEADER}📊 Session Status{Colors.ENDC}")
        print(f"{Colors.OKCYAN}Working Directory:{Colors.ENDC} {self.current_directory}")
        print(f"{Colors.OKCYAN}Active Files:{Colors.ENDC} {len(self.active_files)}")
        print(f"{Colors.OKCYAN}Conversation History:{Colors.ENDC} {len(self.session_history)} exchanges")
        print(f"{Colors.OKCYAN}Commands Executed:{Colors.ENDC} {len(self.executed_commands)}")
        print(f"{Colors.OKCYAN}Pending Changes:{Colors.ENDC} {len(self.pending_changes)}")

        if self.active_files:
            print(f"\n{Colors.BOLD}Active Files:{Colors.ENDC}")
            for file_path in sorted(self.active_files):
                status = "📄" if os.path.exists(file_path) else "❓"
                print(f"  {status} {file_path}")

    def show_history(self):
        """Show conversation history"""
        if not self.session_history:
            print(f"{Colors.WARNING}No conversation history{Colors.ENDC}")
            return

        print(f"\n{Colors.HEADER}📜 Conversation History{Colors.ENDC}")
        for i, entry in enumerate(self.session_history[-5:], 1):  # Show last 5
            timestamp = entry.get('timestamp', 'Unknown')
            print(f"\n{Colors.BOLD}Exchange {i} ({timestamp}):{Colors.ENDC}")
            print(f"{Colors.OKGREEN}You:{Colors.ENDC} {entry['user'][:100]}{'...' if len(entry['user']) > 100 else ''}")
            print(f"{Colors.OKBLUE}Agent:{Colors.ENDC} {entry['assistant'][:200]}{'...' if len(entry['assistant']) > 200 else ''}")

    def show_files(self):
        """Show files in current directory"""
        print(f"\n{Colors.HEADER}📁 Files in {self.current_directory}{Colors.ENDC}")

        try:
            items = sorted(os.listdir('.'))
            for item in items:
                if os.path.isdir(item):
                    icon = "📁"
                    color = Colors.OKCYAN
                elif any(item.endswith(ext) for ext in self.config["included_extensions"]):
                    icon = "📄"
                    color = Colors.OKGREEN if item in self.active_files else Colors.ENDC
                else:
                    icon = "📄"
                    color = Colors.ENDC

                print(f"  {icon} {color}{item}{Colors.ENDC}")
        except Exception as e:
            print(f"{Colors.FAIL}Error listing files: {e}{Colors.ENDC}")

    def handle_backup_command(self, args: List[str]):
        """Handle backup operations"""
        if not args:
            print(f"{Colors.FAIL}Usage: backup <list|restore> [file]{Colors.ENDC}")
            return

        operation = args[0].lower()
        backup_dir = ".qwen-backups"

        if operation == 'list':
            if os.path.exists(backup_dir):
                backups = os.listdir(backup_dir)
                if backups:
                    print(f"\n{Colors.HEADER}💾 Available Backups:{Colors.ENDC}")
                    for backup in sorted(backups):
                        print(f"  📄 {backup}")
                else:
                    print(f"{Colors.WARNING}No backups found{Colors.ENDC}")
            else:
                print(f"{Colors.WARNING}No backup directory found{Colors.ENDC}")

        elif operation == 'restore' and len(args) > 1:
            backup_file = args[1]
            backup_path = os.path.join(backup_dir, backup_file)

            if os.path.exists(backup_path):
                # Extract original file path from backup name
                original_name = backup_file.replace('_', '/').split('.backup')[0]

                confirm = input(f"{Colors.WARNING}Restore {backup_file} to {original_name}? (y/n): {Colors.ENDC}").lower().strip()
                if confirm in ['y', 'yes']:
                    try:
                        shutil.copy2(backup_path, original_name)
                        print(f"{Colors.OKGREEN}✓ Restored {original_name} from backup{Colors.ENDC}")
                    except Exception as e:
                        print(f"{Colors.FAIL}✗ Error restoring backup: {e}{Colors.ENDC}")
            else:
                print(f"{Colors.FAIL}Backup file not found: {backup_file}{Colors.ENDC}")

    def run(self):
        """Main application loop"""
        try:
            while True:
                try:
                    user_input = input(f"\n{Colors.BOLD}qwen-agent>{Colors.ENDC} ").strip()

                    if not self.handle_command(user_input):
                        break

                except KeyboardInterrupt:
                    print(f"\n{Colors.OKCYAN}Use 'exit' to quit{Colors.ENDC}")
                except EOFError:
                    print(f"\n{Colors.OKGREEN}👋 Goodbye!{Colors.ENDC}")
                    break

        except Exception as e:
            print(f"{Colors.FAIL}Unexpected error: {e}{Colors.ENDC}")

def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description="Qwen Coder Agent - Intelligent Coding Assistant")
    parser.add_argument("--config", help="Path to config file", default="~/.qwen-coder-config.json")
    parser.add_argument("--auto-apply", action="store_true", help="Auto-apply file changes")
    parser.add_argument("--no-backup", action="store_true", help="Disable file backups")
    parser.add_argument("--api-key", help="Hugging Face API key")
    parser.add_argument("--model", help="Model to use", default="Qwen/Qwen2.5-Coder-32B-Instruct")

    args = parser.parse_args()

    # Create agent instance
    agent = QwenCoderAgent(args.config)

    # Override config with command line arguments
    if args.auto_apply:
        agent.config["auto_apply_changes"] = True
    if args.no_backup:
        agent.config["backup_files"] = False
    if args.api_key:
        agent.config["api_key"] = args.api_key
    if args.model:
        agent.config["model"] = args.model
        agent.config["base_url"] = f"https://api-inference.huggingface.co/models/{args.model}"

    # Check API key
    if not agent.config["api_key"]:
        print(f"{Colors.WARNING}⚠️  No API key configured!{Colors.ENDC}")
        print(f"{Colors.OKCYAN}Set HF_TOKEN environment variable or use --api-key option{Colors.ENDC}")
        print(f"{Colors.OKCYAN}Get your free token at: https://huggingface.co/settings/tokens{Colors.ENDC}")

    # Run the agent
    agent.run()

if __name__ == "__main__":
    main()
