#!/usr/bin/env python3
"""
Professional Qwen3-Coder Agent
Using proper Hugging Face InferenceClient and Router API
"""

import os
import sys
import json
import subprocess
import time
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from datetime import datetime

try:
    import openai
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False
    print("Installing openai...")
    subprocess.check_call([sys.executable, "-m", "pip", "install", "openai"])
    import openai
    OPENAI_AVAILABLE = True

@dataclass
class AgentConfig:
    name: str
    description: str
    model: str = "Qwen/Qwen3-Coder-480B-A35B-Instruct:novita"
    system_prompt: str = ""
    max_tokens: int = 4096
    temperature: float = 0.7
    stream: bool = True

class ProfessionalAgent:
    """Professional agent using proper HF infrastructure"""
    
    def __init__(self, config: AgentConfig):
        self.config = config
        self.conversation_history = []
        self.working_dir = os.getcwd()
        self.session_id = f"session_{int(time.time())}"
        
        # Initialize HF client
        api_key = os.getenv("HF_TOKEN")
        if not api_key:
            print("❌ No HF_TOKEN found. Set it with:")
            print("PowerShell: $env:HF_TOKEN=\"your_token\"")
            print("CMD: set HF_TOKEN=your_token")
            sys.exit(1)
        
        try:
            self.client = openai.OpenAI(
                base_url="https://router.huggingface.co/v1",
                api_key=api_key
            )
            print(f"✅ {self.config.name} initialized successfully!")
            print(f"🤖 Model: {self.config.model}")
            print(f"📁 Working in: {self.working_dir}")
        except Exception as e:
            print(f"❌ Failed to initialize client: {e}")
            sys.exit(1)

    def chat(self, message: str, stream: bool = None) -> str:
        """Chat with the agent"""
        if stream is None:
            stream = self.config.stream
        
        # Build messages with system prompt and history
        messages = []
        
        # Add system message
        if self.config.system_prompt:
            messages.append({
                "role": "system",
                "content": self.config.system_prompt
            })
        
        # Add conversation history (last 10 exchanges)
        for entry in self.conversation_history[-20:]:  # Last 10 exchanges (20 messages)
            messages.append(entry)
        
        # Add current message
        messages.append({
            "role": "user", 
            "content": message
        })
        
        try:
            if stream:
                return self._stream_response(messages)
            else:
                return self._single_response(messages)
        except Exception as e:
            return f"❌ Error: {e}"

    def _stream_response(self, messages: List[Dict]) -> str:
        """Handle streaming response"""
        print("🤖 ", end="", flush=True)

        try:
            stream = self.client.chat.completions.create(
                model=self.config.model,
                messages=messages,
                max_tokens=self.config.max_tokens,
                temperature=self.config.temperature,
                stream=True
            )

            full_response = ""
            for chunk in stream:
                if chunk.choices and chunk.choices[0].delta and chunk.choices[0].delta.content:
                    content = chunk.choices[0].delta.content
                    print(content, end="", flush=True)
                    full_response += content

            print()  # New line after streaming

            # Add to conversation history
            self.conversation_history.append({"role": "user", "content": messages[-1]["content"]})
            self.conversation_history.append({"role": "assistant", "content": full_response})

            return full_response

        except Exception as e:
            print(f"\n❌ Streaming error: {e}")
            return str(e)

    def _single_response(self, messages: List[Dict]) -> str:
        """Handle single response"""
        try:
            completion = self.client.chat.completions.create(
                model=self.config.model,
                messages=messages,
                max_tokens=self.config.max_tokens,
                temperature=self.config.temperature
            )

            response = completion.choices[0].message.content

            # Add to conversation history
            self.conversation_history.append({"role": "user", "content": messages[-1]["content"]})
            self.conversation_history.append({"role": "assistant", "content": response})

            return response

        except Exception as e:
            return f"❌ Error: {e}"

    def execute_command(self, command: str) -> Dict[str, Any]:
        """Execute system command safely"""
        if not self._is_safe_command(command):
            return {
                "success": False,
                "output": "",
                "error": "Command blocked for safety"
            }
        
        try:
            print(f"🔧 Executing: {command}")
            result = subprocess.run(
                command,
                shell=True,
                capture_output=True,
                text=True,
                timeout=60,
                cwd=self.working_dir
            )
            
            return {
                "success": result.returncode == 0,
                "output": result.stdout,
                "error": result.stderr,
                "exit_code": result.returncode
            }
            
        except subprocess.TimeoutExpired:
            return {
                "success": False,
                "output": "",
                "error": "Command timed out"
            }
        except Exception as e:
            return {
                "success": False,
                "output": "",
                "error": str(e)
            }

    def _is_safe_command(self, command: str) -> bool:
        """Check if command is safe"""
        dangerous = [
            "rm -rf", "del /s", "format", "mkfs", "dd if=",
            "sudo rm", "> /dev/", "chmod 777", "shutdown", "reboot"
        ]
        return not any(danger in command.lower() for danger in dangerous)

    def create_file(self, path: str, content: str) -> bool:
        """Create file with content"""
        try:
            os.makedirs(os.path.dirname(path) or '.', exist_ok=True)
            with open(path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ Created: {path}")
            return True
        except Exception as e:
            print(f"❌ Error creating {path}: {e}")
            return False

    def read_file(self, path: str) -> Optional[str]:
        """Read file content"""
        try:
            with open(path, 'r', encoding='utf-8') as f:
                content = f.read()
            print(f"📖 Read: {path} ({len(content)} chars)")
            return content
        except Exception as e:
            print(f"❌ Error reading {path}: {e}")
            return None

    def analyze_project(self) -> str:
        """Analyze current project structure"""
        analysis = []
        
        # Get project files
        try:
            files = []
            for root, dirs, filenames in os.walk('.'):
                # Skip hidden and common ignore directories
                dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['node_modules', '__pycache__', 'dist', 'build']]
                
                for filename in filenames:
                    if not filename.startswith('.'):
                        rel_path = os.path.relpath(os.path.join(root, filename))
                        files.append(rel_path)
            
            analysis.append(f"📁 Project Structure ({len(files)} files):")
            for file in sorted(files)[:20]:  # Show first 20 files
                analysis.append(f"  {file}")
            
            if len(files) > 20:
                analysis.append(f"  ... and {len(files) - 20} more files")
            
        except Exception as e:
            analysis.append(f"❌ Error analyzing project: {e}")
        
        return "\n".join(analysis)

    def get_status(self) -> Dict[str, Any]:
        """Get agent status"""
        return {
            "name": self.config.name,
            "model": self.config.model,
            "session_id": self.session_id,
            "working_directory": self.working_dir,
            "conversation_length": len(self.conversation_history),
            "uptime": datetime.now().isoformat()
        }

def create_coding_agent() -> ProfessionalAgent:
    """Create a coding-focused agent"""
    config = AgentConfig(
        name="Qwen3 Coder Pro",
        description="Professional coding assistant powered by Qwen3-Coder-480B",
        model="Qwen/Qwen3-Coder-480B-A35B-Instruct:novita",
        system_prompt="""You are Qwen3 Coder Pro, an expert programming assistant powered by the 480B parameter Qwen3-Coder model.

You excel at:
- Writing clean, efficient, and well-documented code
- Debugging complex issues and providing solutions
- Code review and optimization suggestions
- Explaining programming concepts clearly
- Working with multiple programming languages and frameworks
- Repository-level understanding and analysis

Always provide:
- Working, tested code examples
- Clear explanations of your solutions
- Best practices and security considerations
- Proper error handling

Be practical, direct, and focus on real-world solutions.""",
        max_tokens=4096,
        temperature=0.7,
        stream=True
    )
    
    return ProfessionalAgent(config)

def main():
    """Main CLI interface"""
    print("🚀 Professional Qwen3-Coder Agent")
    print("Powered by Hugging Face Router API")
    print("=" * 50)
    
    # Create agent
    agent = create_coding_agent()
    
    print(f"\n🎯 {agent.config.name} is ready!")
    print("Commands:")
    print("  'exit' - Quit the agent")
    print("  'status' - Show agent status")
    print("  'analyze' - Analyze current project")
    print("  '!command' - Execute system command")
    print("  '/create filename' - Create file interactively")
    print("  '/read filename' - Read file content")
    print("  Or just chat normally for coding help!")
    print("-" * 50)
    
    while True:
        try:
            user_input = input(f"\n{agent.config.name}> ").strip()
            
            if user_input.lower() in ['exit', 'quit', 'bye']:
                print("👋 Happy coding!")
                break
            
            elif user_input.lower() == 'status':
                status = agent.get_status()
                print("\n📊 Agent Status:")
                for key, value in status.items():
                    print(f"  {key}: {value}")
            
            elif user_input.lower() == 'analyze':
                analysis = agent.analyze_project()
                print(f"\n{analysis}")
            
            elif user_input.startswith('!'):
                command = user_input[1:]
                result = agent.execute_command(command)
                if result['success']:
                    if result['output']:
                        print(f"✅ Output:\n{result['output']}")
                    else:
                        print("✅ Command executed successfully")
                else:
                    print(f"❌ Error: {result['error']}")
            
            elif user_input.startswith('/create '):
                filename = user_input[8:].strip()
                print(f"Creating {filename}. Enter content (Ctrl+Z then Enter on Windows, Ctrl+D on Unix to finish):")
                content_lines = []
                try:
                    while True:
                        line = input()
                        content_lines.append(line)
                except EOFError:
                    content = '\n'.join(content_lines)
                    agent.create_file(filename, content)
            
            elif user_input.startswith('/read '):
                filename = user_input[6:].strip()
                content = agent.read_file(filename)
                if content:
                    print(f"\n📄 {filename}:")
                    print(content)
            
            elif user_input:
                # Regular chat
                response = agent.chat(user_input)
                if not agent.config.stream:  # Only print if not streaming
                    print(f"🤖 {response}")
            
        except KeyboardInterrupt:
            print("\n👋 Happy coding!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
