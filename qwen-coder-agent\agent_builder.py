#!/usr/bin/env python3
"""
Production Agent Builder
Real-world ready, enterprise-grade agent system
"""

import os
import sys
import json
import requests
import subprocess
import time
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, asdict
from datetime import datetime
import threading
import queue

@dataclass
class Tool:
    name: str
    description: str
    function: Callable
    parameters: Dict[str, Any] = None

@dataclass
class AgentTemplate:
    name: str
    description: str
    system_prompt: str
    tools: List[str] = None
    model: str = "Qwen/Qwen2.5-Coder-7B-Instruct"
    temperature: float = 0.7
    max_tokens: int = 1000

class ProductionAgent:
    """Enterprise-grade agent with reliability and error handling"""
    
    def __init__(self, template: AgentTemplate, api_key: str):
        self.template = template
        self.api_key = api_key
        self.session_id = f"session_{int(time.time())}"
        self.conversation_history = []
        self.tools = {}
        self.working_dir = os.getcwd()
        self.is_ready = False
        
        # Initialize tools
        self._setup_default_tools()
        
        # Test connection
        if self._test_connection():
            self.is_ready = True
            print(f"✅ Agent '{self.template.name}' is ready!")
        else:
            print(f"❌ Agent '{self.template.name}' failed to initialize")

    def _test_connection(self) -> bool:
        """Test API connection with fallback models"""
        test_models = [
            self.template.model,
            "Qwen/Qwen2.5-Coder-7B-Instruct",
            "Qwen/Qwen2.5-Coder-1.5B-Instruct"
        ]
        
        for model in test_models:
            try:
                response = self._call_api("test", model=model, max_tokens=10)
                if response and not response.startswith("❌"):
                    self.template.model = model  # Use working model
                    return True
            except:
                continue
        
        return False

    def _call_api(self, prompt: str, model: str = None, max_tokens: int = None) -> str:
        """Reliable API call with proper error handling"""
        model = model or self.template.model
        max_tokens = max_tokens or self.template.max_tokens
        
        # Build context
        context = f"""You are {self.template.name}: {self.template.description}

{self.template.system_prompt}

Working directory: {self.working_dir}
Available tools: {', '.join(self.tools.keys())}

Be helpful, practical, and provide working solutions."""

        full_prompt = f"{context}\n\nUser: {prompt}\nAssistant:"
        
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "inputs": full_prompt,
            "parameters": {
                "max_new_tokens": max_tokens,
                "temperature": self.template.temperature,
                "return_full_text": False,
                "do_sample": True
            }
        }
        
        try:
            url = f"https://api-inference.huggingface.co/models/{model}"
            response = requests.post(url, headers=headers, json=payload, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                if isinstance(result, list) and len(result) > 0:
                    return result[0].get("generated_text", "").strip()
                else:
                    return str(result)
            else:
                return f"❌ API Error: {response.status_code}"
                
        except Exception as e:
            return f"❌ Connection Error: {e}"

    def _setup_default_tools(self):
        """Setup default tools for the agent"""
        
        def execute_command(command: str) -> Dict[str, Any]:
            """Execute system command safely"""
            if not self._is_safe_command(command):
                return {"error": "Command blocked for safety"}
            
            try:
                result = subprocess.run(
                    command, shell=True, capture_output=True, 
                    text=True, timeout=30, cwd=self.working_dir
                )
                return {
                    "success": result.returncode == 0,
                    "output": result.stdout,
                    "error": result.stderr
                }
            except Exception as e:
                return {"error": str(e)}
        
        def create_file(path: str, content: str) -> Dict[str, Any]:
            """Create a file with content"""
            try:
                os.makedirs(os.path.dirname(path) or '.', exist_ok=True)
                with open(path, 'w', encoding='utf-8') as f:
                    f.write(content)
                return {"success": True, "message": f"Created {path}"}
            except Exception as e:
                return {"error": str(e)}
        
        def read_file(path: str) -> Dict[str, Any]:
            """Read file content"""
            try:
                with open(path, 'r', encoding='utf-8') as f:
                    content = f.read()
                return {"success": True, "content": content}
            except Exception as e:
                return {"error": str(e)}
        
        def list_files(directory: str = ".") -> Dict[str, Any]:
            """List files in directory"""
            try:
                files = os.listdir(directory)
                return {"success": True, "files": files}
            except Exception as e:
                return {"error": str(e)}
        
        # Register tools
        self.tools = {
            "execute": Tool("execute", "Execute system command", execute_command),
            "create_file": Tool("create_file", "Create file with content", create_file),
            "read_file": Tool("read_file", "Read file content", read_file),
            "list_files": Tool("list_files", "List files in directory", list_files)
        }

    def _is_safe_command(self, command: str) -> bool:
        """Check if command is safe"""
        dangerous = ["rm -rf", "del /s", "format", "mkfs", "dd if="]
        return not any(danger in command.lower() for danger in dangerous)

    def chat(self, message: str) -> str:
        """Main chat interface"""
        if not self.is_ready:
            return "❌ Agent not ready. Check API connection."
        
        # Add to history
        self.conversation_history.append({
            "role": "user", 
            "content": message, 
            "timestamp": datetime.now().isoformat()
        })
        
        # Get response
        response = self._call_api(message)
        
        # Add response to history
        self.conversation_history.append({
            "role": "assistant", 
            "content": response, 
            "timestamp": datetime.now().isoformat()
        })
        
        return response

    def use_tool(self, tool_name: str, **kwargs) -> Dict[str, Any]:
        """Use a tool"""
        if tool_name not in self.tools:
            return {"error": f"Tool '{tool_name}' not found"}
        
        try:
            return self.tools[tool_name].function(**kwargs)
        except Exception as e:
            return {"error": str(e)}

    def get_status(self) -> Dict[str, Any]:
        """Get agent status"""
        return {
            "name": self.template.name,
            "ready": self.is_ready,
            "model": self.template.model,
            "session_id": self.session_id,
            "working_dir": self.working_dir,
            "conversation_length": len(self.conversation_history),
            "available_tools": list(self.tools.keys())
        }

class AgentBuilder:
    """Build and manage production agents"""
    
    def __init__(self):
        self.agents: Dict[str, ProductionAgent] = {}
        self.templates: Dict[str, AgentTemplate] = {}
        self.config_dir = os.path.expanduser("~/.agent-builder")
        os.makedirs(self.config_dir, exist_ok=True)
        
        # Load default templates
        self._create_default_templates()

    def _create_default_templates(self):
        """Create useful default agent templates"""
        
        # Coding Assistant
        self.templates["coder"] = AgentTemplate(
            name="Coding Assistant",
            description="Expert programming assistant",
            system_prompt="""You are an expert programming assistant. You can:
- Write clean, efficient code in any language
- Debug and fix code issues
- Explain complex programming concepts
- Suggest best practices and optimizations
- Help with project structure and architecture

Always provide working, tested code examples."""
        )
        
        # DevOps Assistant
        self.templates["devops"] = AgentTemplate(
            name="DevOps Assistant", 
            description="Infrastructure and deployment expert",
            system_prompt="""You are a DevOps expert. You can:
- Help with CI/CD pipelines
- Configure cloud infrastructure
- Optimize deployment processes
- Troubleshoot system issues
- Suggest monitoring and logging solutions

Focus on practical, production-ready solutions."""
        )
        
        # Data Analyst
        self.templates["analyst"] = AgentTemplate(
            name="Data Analyst",
            description="Data analysis and visualization expert", 
            system_prompt="""You are a data analysis expert. You can:
- Analyze datasets and find insights
- Create visualizations and reports
- Suggest statistical approaches
- Help with data cleaning and preprocessing
- Recommend appropriate tools and libraries

Provide actionable insights from data."""
        )

    def create_agent(self, template_name: str, api_key: str = None) -> Optional[ProductionAgent]:
        """Create agent from template"""
        if template_name not in self.templates:
            print(f"❌ Template '{template_name}' not found")
            return None
        
        api_key = api_key or os.getenv("HF_TOKEN")
        if not api_key:
            print("❌ No API key provided")
            return None
        
        template = self.templates[template_name]
        agent = ProductionAgent(template, api_key)
        
        if agent.is_ready:
            self.agents[template.name] = agent
            return agent
        else:
            return None

    def list_templates(self) -> List[str]:
        """List available templates"""
        return list(self.templates.keys())

    def get_agent(self, name: str) -> Optional[ProductionAgent]:
        """Get agent by name"""
        return self.agents.get(name)

    def create_custom_template(self, name: str, description: str, system_prompt: str) -> AgentTemplate:
        """Create custom agent template"""
        template = AgentTemplate(
            name=name,
            description=description,
            system_prompt=system_prompt
        )
        self.templates[name.lower().replace(" ", "_")] = template
        return template

def main():
    """Main CLI interface"""
    print("🏭 Production Agent Builder")
    print("Enterprise-grade AI agents that actually work")
    print("=" * 50)
    
    # Check API key
    api_key = os.getenv("HF_TOKEN")
    if not api_key:
        print("❌ No HF_TOKEN found. Set it with:")
        print("   set HF_TOKEN=your_token_here")
        return
    
    builder = AgentBuilder()
    
    # Show available templates
    print("📋 Available agent templates:")
    for i, template_name in enumerate(builder.list_templates(), 1):
        template = builder.templates[template_name]
        print(f"  {i}. {template.name} - {template.description}")
    
    # Let user choose
    choice = input("\nChoose template (1-3) or 'custom': ").strip()
    
    if choice == 'custom':
        name = input("Agent name: ").strip()
        description = input("Description: ").strip()
        system_prompt = input("System prompt: ").strip()
        
        template = builder.create_custom_template(name, description, system_prompt)
        agent = ProductionAgent(template, api_key)
    else:
        try:
            template_names = list(builder.templates.keys())
            template_name = template_names[int(choice) - 1]
            agent = builder.create_agent(template_name, api_key)
        except (ValueError, IndexError):
            print("❌ Invalid choice")
            return
    
    if not agent or not agent.is_ready:
        print("❌ Failed to create agent")
        return
    
    # Start interactive session
    print(f"\n🤖 {agent.template.name} is ready!")
    print("Commands: 'exit', 'status', 'tools', or chat normally")
    print("-" * 50)
    
    while True:
        try:
            user_input = input(f"{agent.template.name}> ").strip()
            
            if user_input.lower() in ['exit', 'quit']:
                print("👋 Goodbye!")
                break
            elif user_input.lower() == 'status':
                status = agent.get_status()
                for key, value in status.items():
                    print(f"  {key}: {value}")
            elif user_input.lower() == 'tools':
                print("Available tools:")
                for tool_name, tool in agent.tools.items():
                    print(f"  {tool_name}: {tool.description}")
            elif user_input.startswith('!'):
                # Execute command
                command = user_input[1:]
                result = agent.use_tool("execute", command=command)
                if result.get("success"):
                    print(f"✅ {result.get('output', '')}")
                else:
                    print(f"❌ {result.get('error', 'Command failed')}")
            else:
                # Regular chat
                response = agent.chat(user_input)
                print(f"🤖 {response}")
                
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break

if __name__ == "__main__":
    main()
