#!/usr/bin/env python3
"""
Demo script for Qwen Coder Agent
Shows various capabilities and example usage
"""

import os
import sys
import time

def print_header(title):
    """Print a formatted header"""
    print(f"\n{'='*60}")
    print(f"🎯 {title}")
    print(f"{'='*60}")

def print_step(step, description):
    """Print a demo step"""
    print(f"\n📋 Step {step}: {description}")
    print("-" * 40)

def demo_commands():
    """Show example commands"""
    print_header("Qwen Coder Agent Demo")
    
    print("""
🤖 Welcome to Qwen Coder Agent!

This intelligent coding assistant can help you with:
• Code generation and editing
• Bug fixing and debugging  
• Project analysis and refactoring
• Terminal command execution
• File management and creation
• Git integration and workflow
""")
    
    print_step(1, "Basic Usage Examples")
    
    examples = [
        ("Natural Language Requests", [
            "Create a Python function to calculate fibonacci numbers",
            "Fix the authentication bug in my login system", 
            "Add error handling to my API endpoints",
            "Refactor this React component to use hooks",
            "Write unit tests for the user service"
        ]),
        
        ("File Operations", [
            "edit src/app.py add logging functionality",
            "/create utils/helpers.js",
            "/read config/database.json",
            "analyze src/ for code quality issues"
        ]),
        
        ("Terminal Integration", [
            "run npm test",
            "run git status", 
            "run python -m pytest tests/",
            "run docker build -t myapp ."
        ]),
        
        ("Session Management", [
            "status - Show current session info",
            "history - View conversation history",
            "files - List active files",
            "backup list - Show available backups"
        ])
    ]
    
    for category, commands in examples:
        print(f"\n🔧 {category}:")
        for cmd in commands:
            print(f"   qwen-agent> {cmd}")
    
    print_step(2, "Advanced Features")
    
    advanced_features = [
        "🎯 Context-Aware: Understands your entire project structure",
        "🔍 Repository Analysis: Analyzes codebases for improvements", 
        "💾 Safe Editing: Automatic backups and diff previews",
        "🔄 Git Integration: Aware of branches and commit status",
        "🛡️ Safety Checks: Prevents dangerous command execution",
        "📊 Multi-Language: Supports 358+ programming languages",
        "🔧 Customizable: Configurable models and behavior"
    ]
    
    for feature in advanced_features:
        print(f"   {feature}")
    
    print_step(3, "Getting Started")
    
    print("""
🚀 Quick Start:

1. Get your free API key:
   • Visit: https://huggingface.co/settings/tokens
   • Create a new token (free)
   • Set: export HF_TOKEN="your_token_here"

2. Run the setup:
   python3 setup.py

3. Start the agent:
   python3 qwen-agent.py

4. Try your first command:
   qwen-agent> help
   qwen-agent> Create a hello world function in Python
""")
    
    print_step(4, "Example Workflow")
    
    workflow = [
        "🔍 Analyze current project structure",
        "📝 Edit files with AI assistance", 
        "🧪 Run tests to verify changes",
        "🔧 Fix any issues found",
        "📊 Review and commit changes",
        "🚀 Deploy or continue development"
    ]
    
    print("\nTypical development workflow:")
    for i, step in enumerate(workflow, 1):
        print(f"   {i}. {step}")
    
    print_step(5, "Configuration Options")
    
    config_options = {
        "Model Selection": [
            "Qwen/Qwen2.5-Coder-7B-Instruct (Fast)",
            "Qwen/Qwen2.5-Coder-14B-Instruct (Balanced)", 
            "Qwen/Qwen2.5-Coder-32B-Instruct (Best Quality)"
        ],
        "Behavior Settings": [
            "auto_apply_changes: Auto-apply file modifications",
            "backup_files: Create backups before changes",
            "git_integration: Enable git status awareness",
            "temperature: Control AI creativity (0.1-1.0)"
        ]
    }
    
    for category, options in config_options.items():
        print(f"\n🔧 {category}:")
        for option in options:
            print(f"   • {option}")
    
    print_header("Ready to Start!")
    
    print("""
🎉 You're all set! The Qwen Coder Agent is ready to assist you.

💡 Pro Tips:
• Be specific in your requests for better results
• Use 'analyze' to understand unfamiliar codebases  
• Try 'chat' mode for interactive conversations
• Use 'status' to see what files you're working with
• Check 'history' to review previous interactions

🆓 Completely Free:
• No subscription required
• Uses Hugging Face's free API
• Apache 2.0 licensed
• No data collection

🤝 Need Help?
• Type 'help' in the agent for command reference
• Check README.md for detailed documentation
• All features work offline except AI inference

Happy coding! 🚀
""")

def interactive_demo():
    """Run an interactive demo"""
    print_header("Interactive Demo Mode")
    
    print("""
This will show you how the agent works interactively.
Press Enter to continue through each step...
""")
    
    steps = [
        ("Project Analysis", "analyze"),
        ("File Creation", "/create demo_file.py"),
        ("Code Generation", "Create a Python function to reverse a string"),
        ("File Reading", "/read demo_file.py"),
        ("Status Check", "status")
    ]
    
    for i, (description, command) in enumerate(steps, 1):
        input(f"\nStep {i}: {description} - Press Enter...")
        print(f"Command: {command}")
        print("(This would execute the command in the real agent)")
        time.sleep(1)
    
    print("\n✅ Demo completed! Try the real agent with: python3 qwen-agent.py")

def main():
    """Main demo function"""
    if len(sys.argv) > 1 and sys.argv[1] == "--interactive":
        interactive_demo()
    else:
        demo_commands()

if __name__ == "__main__":
    main()
