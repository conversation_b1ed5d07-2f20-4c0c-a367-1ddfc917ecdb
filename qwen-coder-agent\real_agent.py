#!/usr/bin/env python3
"""
Real-World Production Agent Builder
Enterprise-grade, reliable, and actually useful
"""

import os
import sys
import json
import requests
import subprocess
import time
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from datetime import datetime

@dataclass
class AgentConfig:
    name: str
    description: str
    model: str = "Qwen/Qwen2.5-Coder-7B-Instruct"
    api_key: str = ""
    max_tokens: int = 2048
    temperature: float = 0.7
    system_prompt: str = ""
    tools: List[str] = None
    auto_execute: bool = False

class RealAgent:
    """Production-ready AI agent that actually works"""
    
    def __init__(self, config: AgentConfig):
        self.config = config
        self.session_id = f"session_{int(time.time())}"
        self.conversation = []
        self.working_directory = os.getcwd()
        
        # Validate configuration
        if not self.config.api_key:
            self.config.api_key = os.getenv("HF_TOKEN", "")
        
        if not self.config.api_key:
            print("❌ No API key found. Set HF_TOKEN environment variable.")
            sys.exit(1)
        
        # Test API connection
        if not self._test_connection():
            print("❌ API connection failed. Check your token and model.")
            sys.exit(1)
        
        print(f"✅ Agent '{self.config.name}' ready!")
        print(f"📁 Working in: {self.working_directory}")
        print(f"🤖 Model: {self.config.model}")

    def _test_connection(self) -> bool:
        """Test if API is working"""
        try:
            response = self._call_api("test", max_retries=1)
            return "error" not in response.lower()
        except:
            return False

    def _call_api(self, prompt: str, max_retries: int = 3) -> str:
        """Reliable API call with retries and fallbacks"""
        
        # Build system prompt
        system_context = f"""You are {self.config.name}, {self.config.description}
Working directory: {self.working_directory}
Current time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

{self.config.system_prompt}

Be practical, direct, and provide working solutions."""

        full_prompt = f"{system_context}\n\nUser: {prompt}\nAssistant:"
        
        headers = {
            "Authorization": f"Bearer {self.config.api_key}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "inputs": full_prompt,
            "parameters": {
                "max_new_tokens": self.config.max_tokens,
                "temperature": self.config.temperature,
                "return_full_text": False,
                "do_sample": True
            }
        }
        
        # Try multiple model endpoints for reliability
        models_to_try = [
            self.config.model,
            "Qwen/Qwen2.5-Coder-7B-Instruct",
            "Qwen/Qwen2.5-Coder-1.5B-Instruct",
            "microsoft/DialoGPT-medium"
        ]
        
        for attempt in range(max_retries):
            for model in models_to_try:
                try:
                    url = f"https://api-inference.huggingface.co/models/{model}"
                    response = requests.post(url, headers=headers, json=payload, timeout=30)
                    
                    if response.status_code == 200:
                        result = response.json()
                        if isinstance(result, list) and len(result) > 0:
                            text = result[0].get("generated_text", "")
                            if text and len(text.strip()) > 0:
                                return text.strip()
                    
                except Exception as e:
                    if attempt == max_retries - 1:
                        print(f"⚠️ API attempt failed: {e}")
                    continue
            
            if attempt < max_retries - 1:
                time.sleep(2 ** attempt)  # Exponential backoff
        
        return "❌ API unavailable. Please try again later."

    def chat(self, message: str) -> str:
        """Main chat interface"""
        if not message.strip():
            return "Please provide a message."
        
        # Add to conversation history
        self.conversation.append({"role": "user", "content": message, "timestamp": datetime.now()})
        
        # Get AI response
        print("🤖 Thinking...")
        response = self._call_api(message)
        
        # Add response to history
        self.conversation.append({"role": "assistant", "content": response, "timestamp": datetime.now()})
        
        return response

    def execute_command(self, command: str) -> Dict[str, Any]:
        """Execute system command safely"""
        if not self._is_safe_command(command):
            return {
                "success": False,
                "output": "❌ Command blocked for safety",
                "error": "Potentially dangerous command detected"
            }
        
        try:
            result = subprocess.run(
                command,
                shell=True,
                capture_output=True,
                text=True,
                timeout=30,
                cwd=self.working_directory
            )
            
            return {
                "success": result.returncode == 0,
                "output": result.stdout,
                "error": result.stderr,
                "exit_code": result.returncode
            }
            
        except subprocess.TimeoutExpired:
            return {
                "success": False,
                "output": "",
                "error": "Command timed out after 30 seconds"
            }
        except Exception as e:
            return {
                "success": False,
                "output": "",
                "error": str(e)
            }

    def _is_safe_command(self, command: str) -> bool:
        """Check if command is safe to execute"""
        dangerous_patterns = [
            "rm -rf", "del /s", "format", "mkfs", "dd if=", 
            "sudo rm", "> /dev/", "chmod 777", "wget", "curl"
        ]
        
        command_lower = command.lower()
        return not any(pattern in command_lower for pattern in dangerous_patterns)

    def create_file(self, file_path: str, content: str) -> bool:
        """Create a file with content"""
        try:
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return True
        except Exception as e:
            print(f"❌ Error creating file: {e}")
            return False

    def read_file(self, file_path: str) -> Optional[str]:
        """Read file content"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            print(f"❌ Error reading file: {e}")
            return None

    def list_files(self, directory: str = ".") -> List[str]:
        """List files in directory"""
        try:
            return [f for f in os.listdir(directory) if os.path.isfile(os.path.join(directory, f))]
        except Exception as e:
            print(f"❌ Error listing files: {e}")
            return []

    def get_status(self) -> Dict[str, Any]:
        """Get agent status"""
        return {
            "name": self.config.name,
            "session_id": self.session_id,
            "working_directory": self.working_directory,
            "conversation_length": len(self.conversation),
            "model": self.config.model,
            "uptime": datetime.now().isoformat()
        }

class AgentBuilder:
    """Build and manage multiple agents"""
    
    def __init__(self):
        self.agents: Dict[str, RealAgent] = {}
        self.config_dir = os.path.expanduser("~/.real-agents")
        os.makedirs(self.config_dir, exist_ok=True)

    def create_agent(self, name: str, description: str, **kwargs) -> RealAgent:
        """Create a new agent"""
        config = AgentConfig(
            name=name,
            description=description,
            **kwargs
        )
        
        agent = RealAgent(config)
        self.agents[name] = agent
        
        # Save configuration
        self._save_agent_config(name, config)
        
        return agent

    def _save_agent_config(self, name: str, config: AgentConfig):
        """Save agent configuration"""
        config_file = os.path.join(self.config_dir, f"{name}.json")
        config_dict = {
            "name": config.name,
            "description": config.description,
            "model": config.model,
            "max_tokens": config.max_tokens,
            "temperature": config.temperature,
            "system_prompt": config.system_prompt,
            "tools": config.tools or [],
            "auto_execute": config.auto_execute
        }
        
        with open(config_file, 'w') as f:
            json.dump(config_dict, f, indent=2)

    def load_agent(self, name: str) -> Optional[RealAgent]:
        """Load existing agent"""
        config_file = os.path.join(self.config_dir, f"{name}.json")
        
        if not os.path.exists(config_file):
            return None
        
        try:
            with open(config_file, 'r') as f:
                config_dict = json.load(f)
            
            config = AgentConfig(**config_dict)
            agent = RealAgent(config)
            self.agents[name] = agent
            
            return agent
        except Exception as e:
            print(f"❌ Error loading agent: {e}")
            return None

    def list_agents(self) -> List[str]:
        """List available agents"""
        config_files = [f for f in os.listdir(self.config_dir) if f.endswith('.json')]
        return [f[:-5] for f in config_files]  # Remove .json extension

    def get_agent(self, name: str) -> Optional[RealAgent]:
        """Get agent by name"""
        return self.agents.get(name)

def main():
    """Main CLI interface"""
    print("🚀 Real Agent Builder - Production Ready")
    print("=" * 50)
    
    builder = AgentBuilder()
    
    # Check for existing agents
    existing_agents = builder.list_agents()
    if existing_agents:
        print(f"📋 Existing agents: {', '.join(existing_agents)}")
        
        choice = input("Load existing agent? (y/n): ").lower().strip()
        if choice == 'y':
            agent_name = input("Agent name: ").strip()
            agent = builder.load_agent(agent_name)
            if agent:
                print(f"✅ Loaded agent: {agent_name}")
            else:
                print("❌ Agent not found")
                return
        else:
            # Create new agent
            agent = create_new_agent(builder)
    else:
        # Create first agent
        agent = create_new_agent(builder)
    
    # Start interactive session
    print(f"\n🤖 Agent '{agent.config.name}' is ready!")
    print("Commands: 'exit', 'status', 'help', or just chat normally")
    print("-" * 50)
    
    while True:
        try:
            user_input = input(f"{agent.config.name}> ").strip()
            
            if user_input.lower() in ['exit', 'quit']:
                print("👋 Goodbye!")
                break
            elif user_input.lower() == 'status':
                status = agent.get_status()
                for key, value in status.items():
                    print(f"  {key}: {value}")
            elif user_input.lower() == 'help':
                print_help()
            elif user_input.startswith('!'):
                # Execute command
                command = user_input[1:]
                result = agent.execute_command(command)
                if result['success']:
                    print(f"✅ {result['output']}")
                else:
                    print(f"❌ {result['error']}")
            else:
                # Regular chat
                response = agent.chat(user_input)
                print(f"🤖 {response}")
                
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

def create_new_agent(builder: AgentBuilder) -> RealAgent:
    """Create a new agent interactively"""
    print("\n🔧 Creating new agent...")
    
    name = input("Agent name: ").strip()
    description = input("Agent description: ").strip()
    
    print("\nModel options:")
    print("1. Qwen/Qwen2.5-Coder-7B-Instruct (Recommended)")
    print("2. Qwen/Qwen2.5-Coder-1.5B-Instruct (Faster)")
    print("3. Custom model")
    
    choice = input("Choose (1-3): ").strip()
    
    if choice == "2":
        model = "Qwen/Qwen2.5-Coder-1.5B-Instruct"
    elif choice == "3":
        model = input("Model name: ").strip()
    else:
        model = "Qwen/Qwen2.5-Coder-7B-Instruct"
    
    system_prompt = input("System prompt (optional): ").strip()
    
    return builder.create_agent(
        name=name,
        description=description,
        model=model,
        system_prompt=system_prompt
    )

def print_help():
    """Print help information"""
    print("""
🤖 Real Agent Commands:
  
  Normal chat    - Just type your message
  !<command>     - Execute system command (e.g., !ls, !dir)
  status         - Show agent status
  help           - Show this help
  exit/quit      - Exit the agent
  
Examples:
  > Create a Python function to sort a list
  > !python test.py
  > !git status
  > Write a README file for my project
""")

if __name__ == "__main__":
    main()
