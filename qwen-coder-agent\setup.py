#!/usr/bin/env python3
"""
Setup script for Qwen Coder Agent
"""

import os
import sys
import subprocess
import json
from pathlib import Path

def install_requirements():
    """Install required packages"""
    print("📦 Installing requirements...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Requirements installed successfully")
    except subprocess.CalledProcessError as e:
        print(f"❌ Error installing requirements: {e}")
        return False
    return True

def setup_config():
    """Setup initial configuration"""
    config_path = os.path.expanduser("~/.qwen-coder-config.json")
    
    if os.path.exists(config_path):
        print(f"⚠️  Configuration already exists at {config_path}")
        overwrite = input("Overwrite existing config? (y/n): ").lower().strip()
        if overwrite not in ['y', 'yes']:
            print("Keeping existing configuration")
            return True
    
    print("\n🔧 Setting up configuration...")
    
    # Get API key
    api_key = input("Enter your Hugging Face API key (or press Enter to skip): ").strip()
    if not api_key:
        api_key = os.getenv("HF_TOKEN", "")
    
    # Get model preference
    print("\nAvailable models:")
    print("1. Qwen/Qwen2.5-Coder-7B-Instruct (Faster, less capable)")
    print("2. Qwen/Qwen2.5-Coder-14B-Instruct (Balanced)")
    print("3. Qwen/Qwen2.5-Coder-32B-Instruct (Best quality, slower)")
    
    model_choice = input("Choose model (1-3, default: 2): ").strip()
    models = {
        "1": "Qwen/Qwen2.5-Coder-7B-Instruct",
        "2": "Qwen/Qwen2.5-Coder-14B-Instruct", 
        "3": "Qwen/Qwen2.5-Coder-32B-Instruct"
    }
    model = models.get(model_choice, models["2"])
    
    # Create config
    config = {
        "api_key": api_key,
        "base_url": f"https://api-inference.huggingface.co/models/{model}",
        "model": model,
        "max_tokens": 4096,
        "temperature": 0.7,
        "auto_apply_changes": False,
        "git_integration": True,
        "backup_files": True,
        "context_lines": 50,
        "max_file_size": 100000,
        "excluded_dirs": [".git", "node_modules", "__pycache__", ".vscode", "dist", "build"],
        "included_extensions": [".py", ".js", ".ts", ".jsx", ".tsx", ".java", ".cpp", ".c", ".go", ".rs", ".php", ".rb", ".swift", ".kt", ".scala", ".cs", ".html", ".css", ".scss", ".sass", ".vue", ".svelte", ".md", ".json", ".yaml", ".yml", ".toml", ".xml", ".sql"]
    }
    
    try:
        os.makedirs(os.path.dirname(config_path), exist_ok=True)
        with open(config_path, 'w') as f:
            json.dump(config, f, indent=2)
        print(f"✅ Configuration saved to {config_path}")
    except Exception as e:
        print(f"❌ Error saving configuration: {e}")
        return False
    
    return True

def create_launcher():
    """Create launcher scripts"""
    print("\n🚀 Creating launcher scripts...")
    
    # Unix/Linux/Mac launcher
    launcher_content = f"""#!/bin/bash
# Qwen Coder Agent Launcher
cd "{os.path.dirname(os.path.abspath(__file__))}"
python3 qwen-agent.py "$@"
"""
    
    try:
        with open("qwen-coder", "w") as f:
            f.write(launcher_content)
        os.chmod("qwen-coder", 0o755)
        print("✅ Created Unix launcher: qwen-coder")
    except Exception as e:
        print(f"⚠️  Could not create Unix launcher: {e}")
    
    # Windows launcher
    windows_launcher = f"""@echo off
cd /d "{os.path.dirname(os.path.abspath(__file__))}"
python qwen-agent.py %*
"""
    
    try:
        with open("qwen-coder.bat", "w") as f:
            f.write(windows_launcher)
        print("✅ Created Windows launcher: qwen-coder.bat")
    except Exception as e:
        print(f"⚠️  Could not create Windows launcher: {e}")

def setup_git_ignore():
    """Setup .gitignore for the agent directory"""
    gitignore_content = """# Qwen Coder Agent
.qwen-backups/
*.pyc
__pycache__/
.DS_Store
Thumbs.db
"""
    
    try:
        with open(".gitignore", "w") as f:
            f.write(gitignore_content)
        print("✅ Created .gitignore")
    except Exception as e:
        print(f"⚠️  Could not create .gitignore: {e}")

def main():
    """Main setup function"""
    print("🤖 Qwen Coder Agent Setup")
    print("=" * 40)
    
    # Check Python version
    if sys.version_info < (3, 7):
        print("❌ Python 3.7 or higher is required")
        sys.exit(1)
    
    print(f"✅ Python {sys.version.split()[0]} detected")
    
    # Install requirements
    if not install_requirements():
        print("❌ Setup failed during requirements installation")
        sys.exit(1)
    
    # Setup configuration
    if not setup_config():
        print("❌ Setup failed during configuration")
        sys.exit(1)
    
    # Create launchers
    create_launcher()
    
    # Setup gitignore
    setup_git_ignore()
    
    print("\n🎉 Setup completed successfully!")
    print("\n📖 Quick Start:")
    print("  • Run: python3 qwen-agent.py")
    print("  • Or use: ./qwen-coder (Unix) or qwen-coder.bat (Windows)")
    print("  • Type 'help' for available commands")
    print("  • Get your free API key at: https://huggingface.co/settings/tokens")
    
    if not os.getenv("HF_TOKEN") and not input("\nDo you want to set HF_TOKEN environment variable now? (y/n): ").lower().startswith('y'):
        print("\n⚠️  Remember to set your HF_TOKEN environment variable:")
        print("  export HF_TOKEN='your_token_here'  # Unix/Linux/Mac")
        print("  set HF_TOKEN=your_token_here       # Windows")

if __name__ == "__main__":
    main()
