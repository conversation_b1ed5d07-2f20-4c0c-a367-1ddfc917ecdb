#!/usr/bin/env python3
"""
Qwen Coder Agent - Highly Intelligent Coding Assistant
Inspired by <PERSON>urs<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and Kilo

Features:
- Inline code editing with diff preview
- Terminal command execution
- Repository-wide code understanding
- Interactive chat interface
- File management and creation
- Git integration
- Multi-language support
- Context-aware suggestions
"""

import os
import sys
import json
import subprocess
import requests
import difflib
import argparse
import readline
import glob
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
import tempfile
import shutil

# Color codes for terminal output
class Colors:
    HEADER = '\033[95m'
    OKBLUE = '\033[94m'
    OKCYAN = '\033[96m'
    OKGREEN = '\033[92m'
    WARNING = '\033[93m'
    FAIL = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'

@dataclass
class FileChange:
    file_path: str
    original_content: str
    new_content: str
    change_type: str  # 'create', 'modify', 'delete'

class QwenCoderAgent:
    def __init__(self, config_path: str = "~/.qwen-coder-config.json"):
        self.config_path = os.path.expanduser(config_path)
        self.config = self.load_config()
        self.session_history = []
        self.pending_changes = []
        self.current_directory = os.getcwd()
        self.context_files = []
        
        # Initialize readline for better CLI experience
        readline.set_completer(self.path_completer)
        readline.parse_and_bind("tab: complete")
        
        print(f"{Colors.HEADER}🤖 Qwen Coder Agent - Intelligent Coding Assistant{Colors.ENDC}")
        print(f"{Colors.OKCYAN}Working directory: {self.current_directory}{Colors.ENDC}")
        print(f"{Colors.OKGREEN}Type 'help' for commands or start chatting!{Colors.ENDC}\n")

    def load_config(self) -> Dict:
        """Load configuration from file or create default"""
        default_config = {
            "api_key": os.getenv("HF_TOKEN", ""),
            "base_url": "https://api-inference.huggingface.co/models/Qwen/Qwen2.5-Coder-32B-Instruct",
            "model": "Qwen/Qwen2.5-Coder-32B-Instruct",
            "max_tokens": 2048,
            "temperature": 0.7,
            "auto_apply_changes": False,
            "git_integration": True,
            "backup_files": True
        }
        
        if os.path.exists(self.config_path):
            try:
                with open(self.config_path, 'r') as f:
                    config = json.load(f)
                    return {**default_config, **config}
            except Exception as e:
                print(f"{Colors.WARNING}Warning: Could not load config: {e}{Colors.ENDC}")
        
        return default_config

    def save_config(self):
        """Save current configuration"""
        try:
            os.makedirs(os.path.dirname(self.config_path), exist_ok=True)
            with open(self.config_path, 'w') as f:
                json.dump(self.config, f, indent=2)
        except Exception as e:
            print(f"{Colors.FAIL}Error saving config: {e}{Colors.ENDC}")

    def path_completer(self, text, state):
        """Tab completion for file paths"""
        try:
            matches = glob.glob(text + '*')
            if state < len(matches):
                return matches[state]
        except:
            pass
        return None

    def get_repository_context(self) -> str:
        """Get repository structure and key files for context"""
        context = []
        
        # Get git info if available
        try:
            git_branch = subprocess.check_output(['git', 'branch', '--show-current'], 
                                               stderr=subprocess.DEVNULL).decode().strip()
            context.append(f"Git branch: {git_branch}")
        except:
            pass
        
        # Get project structure
        context.append("\nProject structure:")
        for root, dirs, files in os.walk('.'):
            # Skip hidden directories and common ignore patterns
            dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['node_modules', '__pycache__', 'dist', 'build']]
            
            level = root.replace('.', '').count(os.sep)
            if level > 3:  # Limit depth
                continue
                
            indent = ' ' * 2 * level
            context.append(f"{indent}{os.path.basename(root)}/")
            
            subindent = ' ' * 2 * (level + 1)
            for file in files[:10]:  # Limit files shown
                if not file.startswith('.'):
                    context.append(f"{subindent}{file}")
        
        # Add key files content
        key_files = ['package.json', 'requirements.txt', 'Cargo.toml', 'go.mod', 'README.md']
        for file in key_files:
            if os.path.exists(file):
                try:
                    with open(file, 'r') as f:
                        content = f.read()[:500]  # First 500 chars
                        context.append(f"\n{file}:\n{content}")
                except:
                    pass
        
        return '\n'.join(context)

    def call_qwen_api(self, prompt: str, system_prompt: str = None) -> str:
        """Call Qwen API with the given prompt"""
        if not self.config["api_key"]:
            return "Error: No API key configured. Set HF_TOKEN environment variable or run 'config' command."
        
        headers = {
            "Authorization": f"Bearer {self.config['api_key']}",
            "Content-Type": "application/json"
        }
        
        # Build the full prompt with context
        full_prompt = ""
        if system_prompt:
            full_prompt += f"System: {system_prompt}\n\n"
        
        # Add repository context
        repo_context = self.get_repository_context()
        full_prompt += f"Repository Context:\n{repo_context}\n\n"
        
        # Add recent conversation history
        if self.session_history:
            full_prompt += "Recent conversation:\n"
            for entry in self.session_history[-3:]:  # Last 3 exchanges
                full_prompt += f"User: {entry['user']}\nAssistant: {entry['assistant'][:200]}...\n\n"
        
        full_prompt += f"Current request: {prompt}"
        
        payload = {
            "inputs": full_prompt,
            "parameters": {
                "max_new_tokens": self.config["max_tokens"],
                "temperature": self.config["temperature"],
                "return_full_text": False
            }
        }
        
        try:
            response = requests.post(self.config["base_url"], headers=headers, json=payload, timeout=30)
            response.raise_for_status()
            
            result = response.json()
            if isinstance(result, list) and len(result) > 0:
                return result[0].get("generated_text", "No response generated")
            else:
                return str(result)
                
        except requests.exceptions.RequestException as e:
            return f"API Error: {e}"
        except Exception as e:
            return f"Error: {e}"

    def parse_code_blocks(self, text: str) -> List[Tuple[str, str, str]]:
        """Parse code blocks from AI response"""
        import re
        
        # Pattern to match code blocks with optional file paths
        pattern = r'```(?:(\w+)\s*(?:\n|$))?(?:(?:File:|Path:)\s*([^\n]+)\n)?(.*?)```'
        matches = re.findall(pattern, text, re.DOTALL)
        
        code_blocks = []
        for match in matches:
            language = match[0] or 'text'
            file_path = match[1].strip() if match[1] else None
            code = match[2].strip()
            code_blocks.append((language, file_path, code))
        
        return code_blocks

    def show_diff(self, original: str, new: str, file_path: str):
        """Show colored diff between original and new content"""
        print(f"\n{Colors.HEADER}📝 Changes for {file_path}:{Colors.ENDC}")
        
        original_lines = original.splitlines(keepends=True)
        new_lines = new.splitlines(keepends=True)
        
        diff = difflib.unified_diff(
            original_lines, new_lines,
            fromfile=f"a/{file_path}",
            tofile=f"b/{file_path}",
            lineterm=""
        )
        
        for line in diff:
            if line.startswith('+++') or line.startswith('---'):
                print(f"{Colors.BOLD}{line}{Colors.ENDC}", end='')
            elif line.startswith('@@'):
                print(f"{Colors.OKCYAN}{line}{Colors.ENDC}", end='')
            elif line.startswith('+'):
                print(f"{Colors.OKGREEN}{line}{Colors.ENDC}", end='')
            elif line.startswith('-'):
                print(f"{Colors.FAIL}{line}{Colors.ENDC}", end='')
            else:
                print(line, end='')

    def apply_changes(self, changes: List[FileChange], auto_apply: bool = False):
        """Apply pending changes to files"""
        if not changes:
            print(f"{Colors.WARNING}No changes to apply{Colors.ENDC}")
            return
        
        print(f"\n{Colors.HEADER}📋 Pending changes:{Colors.ENDC}")
        for i, change in enumerate(changes):
            print(f"{i+1}. {change.change_type.title()} {change.file_path}")
        
        if not auto_apply:
            choice = input(f"\n{Colors.OKCYAN}Apply changes? (y/n/preview): {Colors.ENDC}").lower()
            if choice == 'preview' or choice == 'p':
                for change in changes:
                    if change.change_type == 'modify':
                        self.show_diff(change.original_content, change.new_content, change.file_path)
                return
            elif choice != 'y' and choice != 'yes':
                print(f"{Colors.WARNING}Changes cancelled{Colors.ENDC}")
                return
        
        # Backup files if enabled
        if self.config["backup_files"]:
            self.backup_files([c.file_path for c in changes if c.change_type != 'create'])
        
        # Apply changes
        for change in changes:
            try:
                if change.change_type == 'create' or change.change_type == 'modify':
                    os.makedirs(os.path.dirname(change.file_path), exist_ok=True)
                    with open(change.file_path, 'w') as f:
                        f.write(change.new_content)
                    print(f"{Colors.OKGREEN}✓ {change.change_type.title()}d {change.file_path}{Colors.ENDC}")
                elif change.change_type == 'delete':
                    os.remove(change.file_path)
                    print(f"{Colors.OKGREEN}✓ Deleted {change.file_path}{Colors.ENDC}")
            except Exception as e:
                print(f"{Colors.FAIL}✗ Error {change.change_type}ing {change.file_path}: {e}{Colors.ENDC}")

    def backup_files(self, file_paths: List[str]):
        """Create backups of files before modification"""
        backup_dir = ".qwen-backups"
        os.makedirs(backup_dir, exist_ok=True)
        
        for file_path in file_paths:
            if os.path.exists(file_path):
                backup_path = os.path.join(backup_dir, f"{file_path.replace('/', '_')}.backup")
                try:
                    shutil.copy2(file_path, backup_path)
                except Exception as e:
                    print(f"{Colors.WARNING}Warning: Could not backup {file_path}: {e}{Colors.ENDC}")

    def execute_command(self, command: str) -> str:
        """Execute terminal command and return output"""
        try:
            print(f"{Colors.OKCYAN}$ {command}{Colors.ENDC}")
            result = subprocess.run(command, shell=True, capture_output=True, text=True, timeout=30)
            
            output = ""
            if result.stdout:
                output += f"STDOUT:\n{result.stdout}\n"
            if result.stderr:
                output += f"STDERR:\n{result.stderr}\n"
            output += f"Exit code: {result.returncode}"
            
            # Color code the output
            if result.returncode == 0:
                print(f"{Colors.OKGREEN}✓ Command executed successfully{Colors.ENDC}")
            else:
                print(f"{Colors.FAIL}✗ Command failed with exit code {result.returncode}{Colors.ENDC}")
            
            print(output)
            return output
            
        except subprocess.TimeoutExpired:
            error = "Command timed out after 30 seconds"
            print(f"{Colors.FAIL}✗ {error}{Colors.ENDC}")
            return error
        except Exception as e:
            error = f"Error executing command: {e}"
            print(f"{Colors.FAIL}✗ {error}{Colors.ENDC}")
            return error
