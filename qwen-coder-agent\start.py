#!/usr/bin/env python3
"""
Simple, reliable agent starter
Just works - no complex setup needed
"""

import os
import sys
import requests

def check_token():
    """Check if API token works"""
    token = os.getenv("HF_TOKEN")
    if not token:
        print("❌ No HF_TOKEN found")
        print("Set it with: set HF_TOKEN=your_token_here")
        return False
    
    print(f"✅ Token found: ***{token[-4:]}")
    return True

def test_api():
    """Test if API is working"""
    token = os.getenv("HF_TOKEN")
    
    # Try simple models that usually work
    models = [
        "Qwen/Qwen2.5-Coder-7B-Instruct",
        "Qwen/Qwen2.5-Coder-1.5B-Instruct",
        "microsoft/DialoGPT-medium"
    ]
    
    for model in models:
        try:
            url = f"https://api-inference.huggingface.co/models/{model}"
            headers = {"Authorization": f"Bearer {token}"}
            payload = {
                "inputs": "Hello",
                "parameters": {"max_new_tokens": 10}
            }
            
            response = requests.post(url, headers=headers, json=payload, timeout=10)
            
            if response.status_code == 200:
                print(f"✅ Working model: {model}")
                return model
            else:
                print(f"⚠️ Model {model}: {response.status_code}")
                
        except Exception as e:
            print(f"⚠️ Model {model}: {e}")
    
    print("❌ No working models found")
    return None

def simple_chat(model):
    """Simple chat interface that actually works"""
    token = os.getenv("HF_TOKEN")
    
    print(f"\n🤖 Simple Agent Ready!")
    print(f"Model: {model}")
    print("Type 'exit' to quit, or just chat normally")
    print("-" * 50)
    
    while True:
        try:
            user_input = input("You: ").strip()
            
            if user_input.lower() in ['exit', 'quit', 'bye']:
                print("👋 Goodbye!")
                break
            
            if not user_input:
                continue
            
            # Call API
            print("🤖 Thinking...")
            
            url = f"https://api-inference.huggingface.co/models/{model}"
            headers = {"Authorization": f"Bearer {token}"}
            
            # Simple prompt that works
            prompt = f"User: {user_input}\nAssistant:"
            
            payload = {
                "inputs": prompt,
                "parameters": {
                    "max_new_tokens": 200,
                    "temperature": 0.7,
                    "return_full_text": False
                }
            }
            
            try:
                response = requests.post(url, headers=headers, json=payload, timeout=30)
                
                if response.status_code == 200:
                    result = response.json()
                    if isinstance(result, list) and len(result) > 0:
                        text = result[0].get("generated_text", "No response")
                        print(f"🤖 {text.strip()}")
                    else:
                        print(f"🤖 {result}")
                else:
                    print(f"❌ API Error: {response.status_code}")
                    print(f"Response: {response.text}")
                    
            except Exception as e:
                print(f"❌ Error: {e}")
                
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break

def main():
    """Main function - keep it simple"""
    print("🚀 Simple Agent - Just Works")
    print("=" * 40)

    # Check for token in command line args
    if len(sys.argv) > 1:
        os.environ["HF_TOKEN"] = sys.argv[1]
        print(f"✅ Token set from command line")

    # Check token
    if not check_token():
        print("\n💡 Try one of these:")
        print("1. PowerShell: $env:HF_TOKEN=\"your_token_here\"")
        print("2. Command Prompt: set HF_TOKEN=your_token_here")
        print("3. Direct: python start.py your_token_here")
        return
    
    # Test API
    working_model = test_api()
    if not working_model:
        print("\n💡 Try these solutions:")
        print("1. Check your token at: https://huggingface.co/settings/tokens")
        print("2. Make sure it's a READ token")
        print("3. Wait a few minutes and try again")
        return
    
    # Start chat
    simple_chat(working_model)

if __name__ == "__main__":
    main()
