#!/usr/bin/env python3
"""
Ultra-simple chat agent that works with any available model
"""

import os
import sys
import requests
import json

def find_working_model(token):
    """Find any working model on Hugging Face"""
    
    # Popular models that are usually available
    models_to_try = [
        "gpt2",
        "distilgpt2", 
        "microsoft/DialoGPT-medium",
        "microsoft/DialoGPT-small",
        "facebook/blenderbot-400M-distill",
        "google/flan-t5-small",
        "Qwen/Qwen2.5-Coder-7B-Instruct",
        "Qwen/Qwen2.5-Coder-1.5B-Instruct",
        "bigscience/bloom-560m",
        "EleutherAI/gpt-neo-125M"
    ]
    
    print("🔍 Testing models...")
    
    for model in models_to_try:
        try:
            url = f"https://api-inference.huggingface.co/models/{model}"
            headers = {"Authorization": f"Bearer {token}"}
            
            # Simple test payload
            payload = {
                "inputs": "Hello",
                "parameters": {"max_new_tokens": 5}
            }
            
            response = requests.post(url, headers=headers, json=payload, timeout=10)
            
            print(f"Testing {model}: {response.status_code}")
            
            if response.status_code == 200:
                # Check if we get a valid response
                try:
                    result = response.json()
                    if result and not isinstance(result, dict) or not result.get('error'):
                        print(f"✅ Working model found: {model}")
                        return model
                except:
                    pass
            elif response.status_code == 503:
                print(f"⏳ Model {model} is loading...")
            
        except Exception as e:
            print(f"❌ {model}: {e}")
    
    return None

def chat_with_model(model, token):
    """Simple chat interface"""
    print(f"\n🤖 Chat Agent Ready!")
    print(f"Model: {model}")
    print("Type 'exit' to quit")
    print("-" * 40)
    
    while True:
        try:
            user_input = input("You: ").strip()
            
            if user_input.lower() in ['exit', 'quit', 'bye']:
                print("👋 Goodbye!")
                break
            
            if not user_input:
                continue
            
            print("🤖 Thinking...")
            
            # Call the API
            url = f"https://api-inference.huggingface.co/models/{model}"
            headers = {"Authorization": f"Bearer {token}"}
            
            # Different payload formats for different models
            if "gpt" in model.lower() or "bloom" in model.lower():
                payload = {
                    "inputs": f"Human: {user_input}\nAI:",
                    "parameters": {
                        "max_new_tokens": 100,
                        "temperature": 0.7,
                        "return_full_text": False
                    }
                }
            elif "blenderbot" in model.lower():
                payload = {
                    "inputs": user_input,
                    "parameters": {
                        "max_length": 100
                    }
                }
            elif "flan" in model.lower():
                payload = {
                    "inputs": f"Answer this question: {user_input}",
                    "parameters": {
                        "max_new_tokens": 100
                    }
                }
            else:
                # Default format
                payload = {
                    "inputs": user_input,
                    "parameters": {
                        "max_new_tokens": 100,
                        "temperature": 0.7
                    }
                }
            
            try:
                response = requests.post(url, headers=headers, json=payload, timeout=30)
                
                if response.status_code == 200:
                    result = response.json()
                    
                    # Handle different response formats
                    if isinstance(result, list) and len(result) > 0:
                        if isinstance(result[0], dict):
                            text = result[0].get("generated_text", "") or result[0].get("translation_text", "") or str(result[0])
                        else:
                            text = str(result[0])
                    elif isinstance(result, dict):
                        text = result.get("generated_text", "") or result.get("translation_text", "") or str(result)
                    else:
                        text = str(result)
                    
                    # Clean up the response
                    if text:
                        # Remove the input prompt if it's included
                        if user_input in text:
                            text = text.replace(user_input, "").strip()
                        if "Human:" in text:
                            text = text.split("Human:")[0].strip()
                        if "AI:" in text:
                            text = text.replace("AI:", "").strip()
                        
                        print(f"🤖 {text}")
                    else:
                        print("🤖 (No response generated)")
                        
                elif response.status_code == 503:
                    print("⏳ Model is loading, please wait and try again...")
                else:
                    print(f"❌ API Error: {response.status_code}")
                    print(f"Response: {response.text[:200]}")
                    
            except Exception as e:
                print(f"❌ Error: {e}")
                
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break

def main():
    """Main function"""
    print("🚀 Simple Chat Agent")
    print("Works with any available Hugging Face model")
    print("=" * 50)
    
    # Get token
    token = os.getenv("HF_TOKEN")
    if len(sys.argv) > 1:
        token = sys.argv[1]
    
    if not token:
        print("❌ No token found!")
        print("Usage:")
        print("  PowerShell: $env:HF_TOKEN=\"your_token\"; python simple_chat.py")
        print("  CMD: set HF_TOKEN=your_token && python simple_chat.py")
        print("  Direct: python simple_chat.py your_token_here")
        return
    
    print(f"✅ Token: ***{token[-4:]}")
    
    # Find working model
    model = find_working_model(token)
    
    if not model:
        print("\n❌ No working models found!")
        print("This could mean:")
        print("1. Your token doesn't have the right permissions")
        print("2. Hugging Face API is having issues")
        print("3. Your internet connection is blocked")
        print("\nTry:")
        print("- Check your token at: https://huggingface.co/settings/tokens")
        print("- Make sure it's a READ token")
        print("- Try again in a few minutes")
        return
    
    # Start chat
    chat_with_model(model, token)

if __name__ == "__main__":
    main()
