# 🤖 Qwen Coder Agent

A highly intelligent coding assistant inspired by <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. This agent provides advanced code editing, terminal command execution, and AI-powered development assistance using Qwen3-Coder models.

## ✨ Features

### 🔧 Core Capabilities
- **Inline Code Editing** - Edit files with AI assistance and diff preview
- **Terminal Integration** - Execute commands with safety checks and output capture
- **Repository Analysis** - Understand entire codebases and project structure
- **Interactive Chat** - Natural language coding conversations
- **File Management** - Create, read, edit, and delete files intelligently
- **Git Integration** - Automatic git status and branch awareness
- **Multi-language Support** - Works with 358+ programming languages

### 🎯 Advanced Features
- **Context-Aware Suggestions** - Understands your project structure
- **Automatic Backups** - Safe file modifications with rollback capability
- **Smart Command Execution** - Safety checks for dangerous operations
- **Session Management** - Persistent conversation history and file tracking
- **Diff Previews** - See exactly what changes before applying
- **Batch Operations** - Apply multiple changes efficiently

### 🚀 Inspired by the Best
- **Cursor-style** inline editing with AI assistance
- **Augment-like** repository-wide understanding
- **Cline-inspired** agentic file operations
- **Kilo-style** efficient terminal integration

## 🛠️ Installation

### Quick Setup
```bash
# Clone or download the agent
cd qwen-coder-agent

# Run setup script
python setup.py

# Start the agent
python qwen_agent.py
```

### Manual Setup
```bash
# Install dependencies
pip install -r requirements.txt

# Get your free API key from https://huggingface.co/settings/tokens
# Windows:
set HF_TOKEN=your_huggingface_token_here
# Unix/Linux/Mac:
export HF_TOKEN="your_huggingface_token_here"

# Run the agent
python qwen_agent.py
```

## 🎮 Usage

### Quick Start Commands
```bash
# Start the agent
python3 qwen-agent.py

# Or use the launcher (after setup)
./qwen-coder              # Unix/Linux/Mac
qwen-coder.bat            # Windows
```

### Command Reference

#### File Operations
```bash
edit <file> [description]     # Edit file with AI assistance
/create <file>               # Create new file
/read <file> [start:end]     # Read file content
/delete <file>              # Delete file
```

#### Code Operations
```bash
analyze [file/directory]     # Analyze code structure
run <command>               # Execute terminal command
chat                        # Enter interactive chat mode
```

#### Session Management
```bash
status                      # Show current session status
history                     # Show conversation history
files                       # Show active files
clear                       # Clear screen
```

#### Configuration
```bash
config                      # Show current configuration
config set <key> <value>    # Set configuration value
backup list                 # List available backups
backup restore <file>       # Restore from backup
```

### Natural Language Usage

Just type your requests in natural language:

```bash
qwen-agent> Create a Python function to sort a list
qwen-agent> Fix the bug in my authentication code
qwen-agent> Add error handling to the API endpoints
qwen-agent> Refactor this component to use React hooks
qwen-agent> Write unit tests for the user service
qwen-agent> Optimize this database query
```

## 🎯 Example Workflows

### 1. Code Review and Improvement
```bash
qwen-agent> analyze src/
qwen-agent> edit src/api.js add better error handling
qwen-agent> run npm test
```

### 2. Bug Fixing
```bash
qwen-agent> /read src/auth.js
qwen-agent> The login function is not working, fix the JWT validation
qwen-agent> run node test-auth.js
```

### 3. Feature Development
```bash
qwen-agent> Create a new user registration endpoint with validation
qwen-agent> Add rate limiting to the API
qwen-agent> Write integration tests for the new endpoint
```

### 4. Project Setup
```bash
qwen-agent> Initialize a new Express.js project with TypeScript
qwen-agent> Add authentication middleware
qwen-agent> Set up database connection with Prisma
```

## ⚙️ Configuration

The agent uses a configuration file at `~/.qwen-coder-config.json`:

```json
{
  "api_key": "your_hf_token",
  "model": "Qwen/Qwen2.5-Coder-32B-Instruct",
  "max_tokens": 4096,
  "temperature": 0.7,
  "auto_apply_changes": false,
  "git_integration": true,
  "backup_files": true,
  "context_lines": 50,
  "max_file_size": 100000
}
```

### Available Models
- `Qwen/Qwen2.5-Coder-7B-Instruct` - Faster, good for simple tasks
- `Qwen/Qwen2.5-Coder-14B-Instruct` - Balanced performance
- `Qwen/Qwen2.5-Coder-32B-Instruct` - Best quality, slower

## 🔒 Safety Features

### Command Safety
- Automatic detection of dangerous commands
- Confirmation prompts for risky operations
- Safe execution environment

### File Safety
- Automatic backups before modifications
- Diff previews before applying changes
- Rollback capability with backup restore

### Project Safety
- Git integration with status awareness
- Excluded directories (node_modules, .git, etc.)
- File size limits to prevent issues

## 🆓 Free Usage

The agent uses Hugging Face's free inference API:

1. **Get Free API Key**: Visit [huggingface.co/settings/tokens](https://huggingface.co/settings/tokens)
2. **No Cost**: Completely free to use with rate limits
3. **No Installation**: No local model download required
4. **Multiple Models**: Choose from different Qwen models

### Rate Limits
- ~1000 requests per hour per token
- Automatic retry with backoff
- Multiple model fallback options

## 🔧 Advanced Usage

### Custom Prompts
```bash
# System-level configuration
qwen-agent> config set temperature 0.3  # More deterministic
qwen-agent> config set max_tokens 8192  # Longer responses
```

### Batch Operations
```bash
# Multiple file editing
qwen-agent> Refactor all components in src/components/ to use TypeScript
qwen-agent> Add JSDoc comments to all functions in utils/
```

### Project Analysis
```bash
# Deep project understanding
qwen-agent> analyze
qwen-agent> What are the main architectural issues in this codebase?
qwen-agent> Suggest performance improvements for the API
```

## 🤝 Integration

### VS Code Integration
Add to your VS Code tasks.json:
```json
{
  "label": "Qwen Coder Agent",
  "type": "shell",
  "command": "python3",
  "args": ["path/to/qwen-agent.py"],
  "group": "build"
}
```

### Git Hooks
Add to `.git/hooks/pre-commit`:
```bash
#!/bin/bash
python3 /path/to/qwen-agent.py "Review the staged changes for issues"
```

## 📊 Comparison with Other Tools

| Feature | Qwen Agent | Cursor | Cline | Augment |
|---------|------------|--------|-------|---------|
| **Free Usage** | ✅ | ❌ | ✅ | ❌ |
| **Local Files** | ✅ | ✅ | ✅ | ✅ |
| **Terminal** | ✅ | ✅ | ✅ | ✅ |
| **Repository Context** | ✅ | ✅ | ✅ | ✅ |
| **Diff Preview** | ✅ | ✅ | ✅ | ✅ |
| **Backup System** | ✅ | ❌ | ❌ | ✅ |
| **Multi-language** | ✅ | ✅ | ✅ | ✅ |
| **CLI Interface** | ✅ | ❌ | ✅ | ❌ |

## 🐛 Troubleshooting

### Common Issues

**API Key Issues**
```bash
# Check if token is set
echo $HF_TOKEN

# Set token
export HF_TOKEN="your_token_here"

# Or configure directly
qwen-agent> config set api_key your_token_here
```

**Permission Errors**
```bash
# Make launcher executable
chmod +x qwen-coder

# Check Python permissions
python3 --version
```

**Model Errors**
```bash
# Switch to smaller model
qwen-agent> config set model Qwen/Qwen2.5-Coder-7B-Instruct

# Check model availability
curl -H "Authorization: Bearer $HF_TOKEN" \
  https://api-inference.huggingface.co/models/Qwen/Qwen2.5-Coder-7B-Instruct
```

## 📝 License

This project is open source and available under the MIT License.

## 🙏 Acknowledgments

- **Qwen Team** - For the amazing Qwen3-Coder models
- **Hugging Face** - For free model hosting and API
- **Cursor, Augment, Cline, Kilo** - For inspiration and feature ideas

## 🚀 Get Started

```bash
# Quick start
cd qwen-coder-agent
python3 setup.py
python3 qwen-agent.py

# First command
qwen-agent> help
```

Happy coding with your new AI assistant! 🎉
