#!/usr/bin/env python3
"""
Quick Start Script for Qwen Coder Agent
This script helps you get started quickly with minimal setup
"""

import os
import sys
import subprocess

def check_requirements():
    """Check if basic requirements are met"""
    print("🔍 Checking requirements...")
    
    # Check Python version
    if sys.version_info < (3, 7):
        print("❌ Python 3.7+ required")
        return False
    print(f"✅ Python {sys.version.split()[0]}")
    
    # Check if requests is available
    try:
        import requests
        print("✅ requests library available")
    except ImportError:
        print("📦 Installing requests...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "requests"])
            print("✅ requests installed")
        except:
            print("❌ Failed to install requests")
            return False
    
    return True

def get_api_key():
    """Get API key from user or environment"""
    api_key = os.getenv("HF_TOKEN")
    
    if not api_key:
        print("\n🔑 API Key Setup")
        print("You need a free Hugging Face API key to use the agent.")
        print("Get one at: https://huggingface.co/settings/tokens")
        
        api_key = input("\nEnter your HF token (or press Enter to skip): ").strip()
        
        if api_key:
            # Set for current session
            os.environ["HF_TOKEN"] = api_key
            print("✅ API key set for this session")
        else:
            print("⚠️  No API key provided. You can set it later with:")
            print("   export HF_TOKEN='your_token_here'")
    else:
        print(f"✅ API key found: ***{api_key[-4:]}")
    
    return api_key

def main():
    """Quick start main function"""
    print("🚀 Qwen Coder Agent - Quick Start")
    print("=" * 40)
    
    if not check_requirements():
        print("\n❌ Requirements check failed")
        sys.exit(1)
    
    api_key = get_api_key()
    
    print("\n🤖 Starting Qwen Coder Agent...")
    print("Type 'help' for available commands")
    print("Type 'exit' to quit")
    print("-" * 40)
    
    # Import and run the agent
    try:
        from qwen_agent import QwenCoderAgent
        agent = QwenCoderAgent()
        
        if api_key:
            agent.config["api_key"] = api_key
        
        agent.run()
        
    except ImportError:
        print("❌ Could not import qwen_agent module")
        print("Make sure qwen-agent.py is in the same directory")
    except Exception as e:
        print(f"❌ Error starting agent: {e}")

if __name__ == "__main__":
    main()
