#!/usr/bin/env python3
"""
Intelligent Project-Aware Agent
Comprehensive modern AI coding assistant with full project understanding
"""

import os
import sys
import json
import subprocess
import time
import fnmatch
import ast
import re
from pathlib import Path
from typing import Dict, List, Optional, Any, Set
from dataclasses import dataclass, asdict
from datetime import datetime
import threading
import queue

try:
    import openai
except ImportError:
    subprocess.check_call([sys.executable, "-m", "pip", "install", "openai"])
    import openai

@dataclass
class FileInfo:
    path: str
    size: int
    modified: float
    language: str
    content: str = ""
    summary: str = ""
    functions: List[str] = None
    classes: List[str] = None
    imports: List[str] = None
    dependencies: List[str] = None

@dataclass
class ProjectContext:
    root_path: str
    files: Dict[str, FileInfo]
    structure: Dict[str, Any]
    languages: Set[str]
    frameworks: Set[str]
    dependencies: Set[str]
    git_info: Dict[str, str]
    total_files: int
    total_lines: int

class IntelligentAgent:
    """Advanced AI agent with full project understanding"""
    
    def __init__(self):
        self.working_dir = os.getcwd()
        self.project_context: Optional[ProjectContext] = None
        self.conversation_history = []
        self.session_id = f"session_{int(time.time())}"
        
        # File patterns to include/exclude
        self.include_patterns = [
            "*.py", "*.js", "*.ts", "*.jsx", "*.tsx", "*.java", "*.cpp", "*.c", "*.h",
            "*.cs", "*.php", "*.rb", "*.go", "*.rs", "*.swift", "*.kt", "*.scala",
            "*.html", "*.css", "*.scss", "*.sass", "*.vue", "*.svelte",
            "*.json", "*.yaml", "*.yml", "*.toml", "*.xml", "*.md", "*.txt",
            "*.sql", "*.sh", "*.bat", "*.ps1", "*.dockerfile", "Dockerfile",
            "package.json", "requirements.txt", "Cargo.toml", "go.mod", "pom.xml"
        ]
        
        self.exclude_patterns = [
            "node_modules/*", ".git/*", "__pycache__/*", "*.pyc", "dist/*", "build/*",
            ".vscode/*", ".idea/*", "*.log", "*.tmp", ".env", "*.min.js", "*.min.css"
        ]
        
        # Initialize OpenAI client
        api_key = os.getenv("HF_TOKEN")
        if not api_key:
            print("❌ No HF_TOKEN found. Set it with:")
            print("PowerShell: $env:HF_TOKEN=\"your_token\"")
            sys.exit(1)
        
        self.client = openai.OpenAI(
            base_url="https://router.huggingface.co/v1",
            api_key=api_key
        )
        
        print("🧠 Intelligent Project Agent Initializing...")
        self.analyze_project()
        print(f"✅ Agent ready with full project understanding!")

    def analyze_project(self):
        """Comprehensive project analysis"""
        print("🔍 Analyzing project structure...")
        
        files = {}
        languages = set()
        frameworks = set()
        dependencies = set()
        total_lines = 0
        
        # Walk through all files
        for root, dirs, filenames in os.walk(self.working_dir):
            # Filter out excluded directories
            dirs[:] = [d for d in dirs if not any(fnmatch.fnmatch(d, pattern.split('/')[0]) for pattern in self.exclude_patterns)]
            
            for filename in filenames:
                file_path = os.path.join(root, filename)
                rel_path = os.path.relpath(file_path, self.working_dir)
                
                # Check if file should be included
                if self._should_include_file(rel_path):
                    try:
                        stat = os.stat(file_path)
                        language = self._detect_language(filename)
                        languages.add(language)
                        
                        # Read file content for analysis
                        content = ""
                        if stat.st_size < 1024 * 1024:  # Only read files < 1MB
                            try:
                                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                                    content = f.read()
                                    total_lines += len(content.splitlines())
                            except:
                                pass
                        
                        # Analyze file content
                        file_info = FileInfo(
                            path=rel_path,
                            size=stat.st_size,
                            modified=stat.st_mtime,
                            language=language,
                            content=content
                        )
                        
                        # Extract metadata
                        self._analyze_file_content(file_info)
                        files[rel_path] = file_info
                        
                        # Detect frameworks and dependencies
                        self._detect_frameworks_and_deps(file_info, frameworks, dependencies)
                        
                    except Exception as e:
                        print(f"⚠️ Error analyzing {rel_path}: {e}")
        
        # Get git information
        git_info = self._get_git_info()
        
        # Build project structure
        structure = self._build_project_structure(files)
        
        self.project_context = ProjectContext(
            root_path=self.working_dir,
            files=files,
            structure=structure,
            languages=languages,
            frameworks=frameworks,
            dependencies=dependencies,
            git_info=git_info,
            total_files=len(files),
            total_lines=total_lines
        )
        
        print(f"📊 Project Analysis Complete:")
        print(f"   📁 {len(files)} files analyzed")
        print(f"   📝 {total_lines:,} lines of code")
        print(f"   🔤 Languages: {', '.join(sorted(languages))}")
        if frameworks:
            print(f"   🚀 Frameworks: {', '.join(sorted(frameworks))}")

    def _should_include_file(self, file_path: str) -> bool:
        """Check if file should be included in analysis"""
        # Check exclude patterns first
        for pattern in self.exclude_patterns:
            if fnmatch.fnmatch(file_path, pattern):
                return False
        
        # Check include patterns
        filename = os.path.basename(file_path)
        for pattern in self.include_patterns:
            if fnmatch.fnmatch(filename, pattern):
                return True
        
        return False

    def _detect_language(self, filename: str) -> str:
        """Detect programming language from filename"""
        ext = os.path.splitext(filename)[1].lower()
        
        language_map = {
            '.py': 'Python', '.js': 'JavaScript', '.ts': 'TypeScript',
            '.jsx': 'React', '.tsx': 'React TypeScript', '.java': 'Java',
            '.cpp': 'C++', '.c': 'C', '.h': 'C/C++ Header', '.cs': 'C#',
            '.php': 'PHP', '.rb': 'Ruby', '.go': 'Go', '.rs': 'Rust',
            '.swift': 'Swift', '.kt': 'Kotlin', '.scala': 'Scala',
            '.html': 'HTML', '.css': 'CSS', '.scss': 'SCSS', '.sass': 'Sass',
            '.vue': 'Vue.js', '.svelte': 'Svelte', '.json': 'JSON',
            '.yaml': 'YAML', '.yml': 'YAML', '.toml': 'TOML', '.xml': 'XML',
            '.md': 'Markdown', '.sql': 'SQL', '.sh': 'Shell', '.bat': 'Batch',
            '.ps1': 'PowerShell'
        }
        
        if filename.lower() in ['dockerfile', 'makefile', 'rakefile']:
            return filename.title()
        
        return language_map.get(ext, 'Unknown')

    def _analyze_file_content(self, file_info: FileInfo):
        """Analyze file content to extract metadata"""
        if not file_info.content:
            return
        
        content = file_info.content
        language = file_info.language
        
        # Initialize lists
        file_info.functions = []
        file_info.classes = []
        file_info.imports = []
        
        try:
            if language == 'Python':
                self._analyze_python_file(file_info)
            elif language in ['JavaScript', 'TypeScript', 'React', 'React TypeScript']:
                self._analyze_js_ts_file(file_info)
            elif language == 'Java':
                self._analyze_java_file(file_info)
            # Add more language-specific analyzers as needed
            
        except Exception as e:
            pass  # Ignore parsing errors

    def _analyze_python_file(self, file_info: FileInfo):
        """Analyze Python file using AST"""
        try:
            tree = ast.parse(file_info.content)
            
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    file_info.functions.append(node.name)
                elif isinstance(node, ast.ClassDef):
                    file_info.classes.append(node.name)
                elif isinstance(node, ast.Import):
                    for alias in node.names:
                        file_info.imports.append(alias.name)
                elif isinstance(node, ast.ImportFrom):
                    if node.module:
                        file_info.imports.append(node.module)
        except:
            pass

    def _analyze_js_ts_file(self, file_info: FileInfo):
        """Analyze JavaScript/TypeScript file using regex"""
        content = file_info.content
        
        # Find function declarations
        func_patterns = [
            r'function\s+(\w+)',
            r'const\s+(\w+)\s*=\s*(?:async\s+)?\(',
            r'(\w+)\s*:\s*(?:async\s+)?function',
            r'(\w+)\s*=\s*(?:async\s+)?\(',
        ]
        
        for pattern in func_patterns:
            matches = re.findall(pattern, content)
            file_info.functions.extend(matches)
        
        # Find class declarations
        class_matches = re.findall(r'class\s+(\w+)', content)
        file_info.classes.extend(class_matches)
        
        # Find imports
        import_patterns = [
            r'import\s+.*?\s+from\s+[\'"]([^\'"]+)[\'"]',
            r'import\s+[\'"]([^\'"]+)[\'"]',
            r'require\([\'"]([^\'"]+)[\'"]\)'
        ]
        
        for pattern in import_patterns:
            matches = re.findall(pattern, content)
            file_info.imports.extend(matches)

    def _analyze_java_file(self, file_info: FileInfo):
        """Analyze Java file using regex"""
        content = file_info.content
        
        # Find class declarations
        class_matches = re.findall(r'(?:public\s+)?class\s+(\w+)', content)
        file_info.classes.extend(class_matches)
        
        # Find method declarations
        method_matches = re.findall(r'(?:public|private|protected)?\s*(?:static\s+)?(?:\w+\s+)+(\w+)\s*\(', content)
        file_info.functions.extend(method_matches)
        
        # Find imports
        import_matches = re.findall(r'import\s+([^;]+);', content)
        file_info.imports.extend(import_matches)

    def _detect_frameworks_and_deps(self, file_info: FileInfo, frameworks: Set[str], dependencies: Set[str]):
        """Detect frameworks and dependencies from file content"""
        content = file_info.content.lower()
        filename = file_info.path.lower()
        
        # Framework detection
        framework_indicators = {
            'React': ['react', 'jsx', 'tsx', 'usestate', 'useeffect'],
            'Vue.js': ['vue', '.vue', 'v-if', 'v-for'],
            'Angular': ['angular', '@component', '@injectable'],
            'Express.js': ['express', 'app.get', 'app.post'],
            'Django': ['django', 'models.model', 'views.py'],
            'Flask': ['flask', 'app.route', '@app.route'],
            'Spring': ['@controller', '@service', '@repository'],
            'Laravel': ['laravel', 'artisan', 'blade.php'],
            'Rails': ['rails', 'activerecord', 'gemfile'],
            'Next.js': ['next', 'getstaticprops', 'getserversideprops'],
            'Nuxt.js': ['nuxt', 'asyncdata', 'nuxt.config'],
            'Svelte': ['svelte', '.svelte'],
            'FastAPI': ['fastapi', '@app.get', '@app.post'],
            'ASP.NET': ['asp.net', '[httpget]', '[httppost]']
        }
        
        for framework, indicators in framework_indicators.items():
            if any(indicator in content or indicator in filename for indicator in indicators):
                frameworks.add(framework)
        
        # Dependency detection from imports
        for imp in file_info.imports or []:
            dependencies.add(imp.split('.')[0])

    def _get_git_info(self) -> Dict[str, str]:
        """Get git repository information"""
        git_info = {}
        
        try:
            # Get current branch
            result = subprocess.run(['git', 'branch', '--show-current'], 
                                  capture_output=True, text=True, cwd=self.working_dir)
            if result.returncode == 0:
                git_info['branch'] = result.stdout.strip()
            
            # Get remote URL
            result = subprocess.run(['git', 'remote', 'get-url', 'origin'], 
                                  capture_output=True, text=True, cwd=self.working_dir)
            if result.returncode == 0:
                git_info['remote'] = result.stdout.strip()
            
            # Get last commit
            result = subprocess.run(['git', 'log', '-1', '--oneline'], 
                                  capture_output=True, text=True, cwd=self.working_dir)
            if result.returncode == 0:
                git_info['last_commit'] = result.stdout.strip()
            
            # Get status
            result = subprocess.run(['git', 'status', '--porcelain'], 
                                  capture_output=True, text=True, cwd=self.working_dir)
            if result.returncode == 0:
                git_info['status'] = 'clean' if not result.stdout.strip() else 'modified'
                
        except:
            pass
        
        return git_info

    def _build_project_structure(self, files: Dict[str, FileInfo]) -> Dict[str, Any]:
        """Build hierarchical project structure"""
        structure = {}
        
        for file_path in files.keys():
            parts = file_path.split(os.sep)
            current = structure
            
            for part in parts[:-1]:  # Directories
                if part not in current:
                    current[part] = {}
                current = current[part]
            
            # File
            filename = parts[-1]
            current[filename] = {
                'type': 'file',
                'language': files[file_path].language,
                'size': files[file_path].size
            }
        
        return structure

    def get_project_summary(self) -> str:
        """Generate comprehensive project summary"""
        if not self.project_context:
            return "No project context available"

        ctx = self.project_context
        summary = []

        summary.append(f"📁 **Project Overview**")
        summary.append(f"   Root: {ctx.root_path}")
        summary.append(f"   Files: {ctx.total_files:,}")
        summary.append(f"   Lines of Code: {ctx.total_lines:,}")

        if ctx.git_info:
            summary.append(f"\n🌿 **Git Information**")
            for key, value in ctx.git_info.items():
                summary.append(f"   {key.title()}: {value}")

        summary.append(f"\n🔤 **Languages ({len(ctx.languages)})**")
        lang_counts = {}
        for file_info in ctx.files.values():
            lang = file_info.language
            lang_counts[lang] = lang_counts.get(lang, 0) + 1

        for lang, count in sorted(lang_counts.items(), key=lambda x: x[1], reverse=True):
            summary.append(f"   {lang}: {count} files")

        if ctx.frameworks:
            summary.append(f"\n🚀 **Frameworks & Libraries**")
            for framework in sorted(ctx.frameworks):
                summary.append(f"   • {framework}")

        # Key files analysis
        key_files = []
        for path, file_info in ctx.files.items():
            if any(name in path.lower() for name in ['main', 'index', 'app', 'server', 'config']):
                key_files.append(path)

        if key_files:
            summary.append(f"\n🔑 **Key Files**")
            for file in sorted(key_files)[:10]:
                summary.append(f"   • {file}")

        return "\n".join(summary)

    def chat_with_context(self, message: str, include_files: List[str] = None) -> str:
        """Chat with full project context"""
        # Build comprehensive context
        context_parts = []

        # Project summary
        context_parts.append("=== PROJECT CONTEXT ===")
        context_parts.append(self.get_project_summary())

        # Include specific files if requested
        if include_files:
            context_parts.append("\n=== RELEVANT FILES ===")
            for file_path in include_files:
                if file_path in self.project_context.files:
                    file_info = self.project_context.files[file_path]
                    context_parts.append(f"\n--- {file_path} ({file_info.language}) ---")
                    if file_info.content and len(file_info.content) < 10000:
                        context_parts.append(file_info.content)
                    else:
                        context_parts.append(f"[Large file - {file_info.size} bytes]")
                        if file_info.functions:
                            context_parts.append(f"Functions: {', '.join(file_info.functions[:10])}")
                        if file_info.classes:
                            context_parts.append(f"Classes: {', '.join(file_info.classes[:10])}")

        # Recent conversation
        if self.conversation_history:
            context_parts.append("\n=== RECENT CONVERSATION ===")
            for entry in self.conversation_history[-4:]:  # Last 2 exchanges
                context_parts.append(f"{entry['role']}: {entry['content'][:200]}...")

        # Build messages
        messages = [
            {
                "role": "system",
                "content": f"""You are an expert AI coding assistant with full access to the user's project.

CAPABILITIES:
- Full project understanding and context awareness
- Code generation, editing, and refactoring
- Architecture analysis and recommendations
- Bug detection and fixing
- Documentation generation
- Best practices guidance

PROJECT CONTEXT:
{chr(10).join(context_parts)}

INSTRUCTIONS:
- Use the project context to provide accurate, relevant responses
- Reference specific files, functions, and classes when relevant
- Suggest concrete improvements based on the actual codebase
- Provide working code that fits the project's patterns and style
- Be specific and actionable in your recommendations"""
            },
            {
                "role": "user",
                "content": message
            }
        ]

        try:
            print("🧠 Analyzing with full project context...")

            response = self.client.chat.completions.create(
                model="Qwen/Qwen3-Coder-480B-A35B-Instruct:novita",
                messages=messages,
                max_tokens=4096,
                temperature=0.7,
                stream=True
            )

            print("🤖 ", end="", flush=True)
            full_response = ""

            for chunk in response:
                if chunk.choices and chunk.choices[0].delta and chunk.choices[0].delta.content:
                    content = chunk.choices[0].delta.content
                    print(content, end="", flush=True)
                    full_response += content

            print()  # New line

            # Add to conversation history
            self.conversation_history.append({"role": "user", "content": message})
            self.conversation_history.append({"role": "assistant", "content": full_response})

            return full_response

        except Exception as e:
            return f"❌ Error: {e}"

    def find_files(self, query: str) -> List[str]:
        """Find files matching query"""
        if not self.project_context:
            return []

        query_lower = query.lower()
        matches = []

        for file_path, file_info in self.project_context.files.items():
            # Check filename
            if query_lower in file_path.lower():
                matches.append(file_path)
                continue

            # Check content for functions/classes
            if file_info.functions and any(query_lower in func.lower() for func in file_info.functions):
                matches.append(file_path)
                continue

            if file_info.classes and any(query_lower in cls.lower() for cls in file_info.classes):
                matches.append(file_path)
                continue

        return matches[:20]  # Limit results

    def get_file_info(self, file_path: str) -> Optional[FileInfo]:
        """Get detailed file information"""
        if not self.project_context or file_path not in self.project_context.files:
            return None
        return self.project_context.files[file_path]

    def create_file(self, file_path: str, content: str) -> bool:
        """Create new file and update project context"""
        try:
            full_path = os.path.join(self.working_dir, file_path)
            os.makedirs(os.path.dirname(full_path), exist_ok=True)

            with open(full_path, 'w', encoding='utf-8') as f:
                f.write(content)

            # Update project context
            if self.project_context and self._should_include_file(file_path):
                stat = os.stat(full_path)
                language = self._detect_language(os.path.basename(file_path))

                file_info = FileInfo(
                    path=file_path,
                    size=stat.st_size,
                    modified=stat.st_mtime,
                    language=language,
                    content=content
                )

                self._analyze_file_content(file_info)
                self.project_context.files[file_path] = file_info
                self.project_context.languages.add(language)
                self.project_context.total_files += 1
                self.project_context.total_lines += len(content.splitlines())

            print(f"✅ Created: {file_path}")
            return True

        except Exception as e:
            print(f"❌ Error creating {file_path}: {e}")
            return False

    def update_file(self, file_path: str, content: str) -> bool:
        """Update existing file and refresh context"""
        try:
            full_path = os.path.join(self.working_dir, file_path)

            with open(full_path, 'w', encoding='utf-8') as f:
                f.write(content)

            # Update project context
            if self.project_context and file_path in self.project_context.files:
                old_info = self.project_context.files[file_path]
                stat = os.stat(full_path)

                # Update file info
                old_info.content = content
                old_info.size = stat.st_size
                old_info.modified = stat.st_mtime
                old_info.functions = []
                old_info.classes = []
                old_info.imports = []

                # Re-analyze content
                self._analyze_file_content(old_info)

                # Update line count
                old_lines = len(old_info.content.splitlines()) if old_info.content else 0
                new_lines = len(content.splitlines())
                self.project_context.total_lines += (new_lines - old_lines)

            print(f"✅ Updated: {file_path}")
            return True

        except Exception as e:
            print(f"❌ Error updating {file_path}: {e}")
            return False

    def search_code(self, query: str, file_types: List[str] = None) -> List[Dict[str, Any]]:
        """Search for code patterns across the project"""
        if not self.project_context:
            return []

        results = []
        query_lower = query.lower()

        for file_path, file_info in self.project_context.files.items():
            # Filter by file types if specified
            if file_types and file_info.language not in file_types:
                continue

            if not file_info.content:
                continue

            lines = file_info.content.splitlines()
            for i, line in enumerate(lines):
                if query_lower in line.lower():
                    results.append({
                        'file': file_path,
                        'line': i + 1,
                        'content': line.strip(),
                        'language': file_info.language
                    })

        return results[:50]  # Limit results

    def get_dependencies(self) -> Dict[str, List[str]]:
        """Get project dependencies by language"""
        if not self.project_context:
            return {}

        deps_by_lang = {}

        for file_info in self.project_context.files.values():
            if file_info.imports:
                lang = file_info.language
                if lang not in deps_by_lang:
                    deps_by_lang[lang] = set()
                deps_by_lang[lang].update(file_info.imports)

        # Convert sets to sorted lists
        return {lang: sorted(list(deps)) for lang, deps in deps_by_lang.items()}

    def suggest_improvements(self) -> List[str]:
        """Suggest project improvements based on analysis"""
        if not self.project_context:
            return ["No project context available"]

        suggestions = []
        ctx = self.project_context

        # Check for missing important files
        important_files = ['README.md', '.gitignore', 'requirements.txt', 'package.json']
        missing_files = [f for f in important_files if not any(f in path for path in ctx.files.keys())]

        if missing_files:
            suggestions.append(f"📝 Consider adding: {', '.join(missing_files)}")

        # Check for large files
        large_files = [path for path, info in ctx.files.items() if info.size > 100000]
        if large_files:
            suggestions.append(f"📦 Large files detected: {', '.join(large_files[:3])} - consider refactoring")

        # Check for code organization
        if len(ctx.files) > 50 and not any('/' in path for path in ctx.files.keys()):
            suggestions.append("📁 Consider organizing code into subdirectories")

        # Language-specific suggestions
        if 'Python' in ctx.languages:
            if not any('test' in path.lower() for path in ctx.files.keys()):
                suggestions.append("🧪 Consider adding unit tests (pytest recommended)")

        if 'JavaScript' in ctx.languages or 'TypeScript' in ctx.languages:
            if 'package.json' not in [os.path.basename(path) for path in ctx.files.keys()]:
                suggestions.append("📦 Consider adding package.json for dependency management")

        return suggestions if suggestions else ["✅ Project structure looks good!"]

def main():
    """Main CLI interface for intelligent agent"""
    print("🧠 Intelligent Project-Aware AI Agent")
    print("Advanced coding assistant with full project understanding")
    print("=" * 60)

    # Initialize agent
    agent = IntelligentAgent()

    print(f"\n🎯 Agent ready! Type 'help' for commands.")
    print("-" * 60)

    while True:
        try:
            user_input = input(f"\n🧠 Agent> ").strip()

            if user_input.lower() in ['exit', 'quit', 'bye']:
                print("👋 Happy coding!")
                break

            elif user_input.lower() == 'help':
                print("""
🧠 Intelligent Agent Commands:

📊 PROJECT ANALYSIS:
  summary              - Show comprehensive project overview
  analyze              - Re-analyze project structure
  suggest              - Get improvement suggestions
  deps                 - Show project dependencies

🔍 CODE SEARCH & NAVIGATION:
  find <query>         - Find files matching query
  search <pattern>     - Search code patterns across project
  info <file>          - Get detailed file information

📝 FILE OPERATIONS:
  create <file>        - Create new file with content
  edit <file>          - Edit existing file
  read <file>          - Read file content

💬 INTELLIGENT CHAT:
  chat <message>       - Chat with full project context
  ask <question>       - Ask about the project
  explain <concept>    - Get explanations with project examples

🔧 ADVANCED FEATURES:
  refactor <file>      - Get refactoring suggestions
  review <file>        - Code review with recommendations
  test <file>          - Generate test cases
  docs <file>          - Generate documentation

Examples:
  > summary
  > find authentication
  > search "def login"
  > chat "How can I improve the API security?"
  > refactor src/main.py
  > explain "How does the user authentication work?"
""")

            elif user_input.lower() == 'summary':
                print(f"\n{agent.get_project_summary()}")

            elif user_input.lower() == 'analyze':
                print("🔄 Re-analyzing project...")
                agent.analyze_project()
                print("✅ Analysis complete!")

            elif user_input.lower() == 'suggest':
                suggestions = agent.suggest_improvements()
                print("\n💡 Project Improvement Suggestions:")
                for suggestion in suggestions:
                    print(f"   {suggestion}")

            elif user_input.lower() == 'deps':
                deps = agent.get_dependencies()
                print("\n📦 Project Dependencies:")
                for lang, dep_list in deps.items():
                    print(f"\n{lang}:")
                    for dep in dep_list[:20]:  # Show first 20
                        print(f"   • {dep}")

            elif user_input.startswith('find '):
                query = user_input[5:].strip()
                matches = agent.find_files(query)
                if matches:
                    print(f"\n🔍 Found {len(matches)} files matching '{query}':")
                    for match in matches:
                        file_info = agent.get_file_info(match)
                        print(f"   📄 {match} ({file_info.language})")
                else:
                    print(f"❌ No files found matching '{query}'")

            elif user_input.startswith('search '):
                pattern = user_input[7:].strip()
                results = agent.search_code(pattern)
                if results:
                    print(f"\n🔍 Found {len(results)} matches for '{pattern}':")
                    for result in results[:20]:  # Show first 20
                        print(f"   📄 {result['file']}:{result['line']} - {result['content'][:80]}...")
                else:
                    print(f"❌ No code matches found for '{pattern}'")

            elif user_input.startswith('info '):
                file_path = user_input[5:].strip()
                file_info = agent.get_file_info(file_path)
                if file_info:
                    print(f"\n📄 File Information: {file_path}")
                    print(f"   Language: {file_info.language}")
                    print(f"   Size: {file_info.size:,} bytes")
                    print(f"   Lines: {len(file_info.content.splitlines()) if file_info.content else 0:,}")
                    if file_info.functions:
                        print(f"   Functions: {', '.join(file_info.functions[:10])}")
                    if file_info.classes:
                        print(f"   Classes: {', '.join(file_info.classes[:10])}")
                    if file_info.imports:
                        print(f"   Imports: {', '.join(file_info.imports[:10])}")
                else:
                    print(f"❌ File not found: {file_path}")

            elif user_input.startswith('read '):
                file_path = user_input[5:].strip()
                file_info = agent.get_file_info(file_path)
                if file_info and file_info.content:
                    print(f"\n📖 Content of {file_path}:")
                    print("-" * 40)
                    print(file_info.content)
                    print("-" * 40)
                else:
                    print(f"❌ Cannot read file: {file_path}")

            elif user_input.startswith('create '):
                file_path = user_input[7:].strip()
                print(f"Creating {file_path}. Enter content (Ctrl+Z then Enter on Windows, Ctrl+D on Unix to finish):")
                content_lines = []
                try:
                    while True:
                        line = input()
                        content_lines.append(line)
                except EOFError:
                    content = '\n'.join(content_lines)
                    agent.create_file(file_path, content)

            elif user_input.startswith('edit '):
                file_path = user_input[5:].strip()
                file_info = agent.get_file_info(file_path)
                if file_info:
                    print(f"Current content of {file_path}:")
                    print("-" * 40)
                    print(file_info.content)
                    print("-" * 40)
                    print("Enter new content (Ctrl+Z then Enter on Windows, Ctrl+D on Unix to finish):")
                    content_lines = []
                    try:
                        while True:
                            line = input()
                            content_lines.append(line)
                    except EOFError:
                        content = '\n'.join(content_lines)
                        agent.update_file(file_path, content)
                else:
                    print(f"❌ File not found: {file_path}")

            elif user_input.startswith(('chat ', 'ask ', 'explain ')):
                # Extract command and message
                parts = user_input.split(' ', 1)
                if len(parts) > 1:
                    message = parts[1]

                    # Add context based on command
                    if user_input.startswith('explain '):
                        message = f"Explain this concept in the context of my project: {message}"
                    elif user_input.startswith('ask '):
                        message = f"Answer this question about my project: {message}"

                    response = agent.chat_with_context(message)
                else:
                    print("❌ Please provide a message")

            elif user_input.startswith('refactor '):
                file_path = user_input[9:].strip()
                if agent.get_file_info(file_path):
                    message = f"Analyze {file_path} and provide specific refactoring suggestions to improve code quality, performance, and maintainability. Include concrete code examples."
                    response = agent.chat_with_context(message, include_files=[file_path])
                else:
                    print(f"❌ File not found: {file_path}")

            elif user_input.startswith('review '):
                file_path = user_input[7:].strip()
                if agent.get_file_info(file_path):
                    message = f"Perform a comprehensive code review of {file_path}. Check for bugs, security issues, performance problems, and adherence to best practices. Provide specific recommendations."
                    response = agent.chat_with_context(message, include_files=[file_path])
                else:
                    print(f"❌ File not found: {file_path}")

            elif user_input.startswith('test '):
                file_path = user_input[5:].strip()
                if agent.get_file_info(file_path):
                    message = f"Generate comprehensive unit tests for {file_path}. Include test cases for normal operation, edge cases, and error conditions. Use appropriate testing framework for the language."
                    response = agent.chat_with_context(message, include_files=[file_path])
                else:
                    print(f"❌ File not found: {file_path}")

            elif user_input.startswith('docs '):
                file_path = user_input[5:].strip()
                if agent.get_file_info(file_path):
                    message = f"Generate comprehensive documentation for {file_path}. Include API documentation, usage examples, and explanations of key functions and classes."
                    response = agent.chat_with_context(message, include_files=[file_path])
                else:
                    print(f"❌ File not found: {file_path}")

            elif user_input:
                # Default to intelligent chat
                response = agent.chat_with_context(user_input)

        except KeyboardInterrupt:
            print("\n👋 Happy coding!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
