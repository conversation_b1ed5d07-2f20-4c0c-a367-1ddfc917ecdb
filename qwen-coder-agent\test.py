#!/usr/bin/env python3
"""
Test script for Qwen Coder Agent
Verifies installation and basic functionality
"""

import os
import sys
import json
import tempfile
import shutil
from pathlib import Path

def test_imports():
    """Test if all required modules can be imported"""
    print("🧪 Testing imports...")
    
    try:
        import requests
        print("✅ requests")
    except ImportError:
        print("❌ requests - run: pip install requests")
        return False
    
    try:
        import readline
        print("✅ readline")
    except ImportError:
        print("⚠️  readline not available (optional)")
    
    try:
        import argparse
        print("✅ argparse")
    except ImportError:
        print("❌ argparse")
        return False
    
    return True

def test_agent_import():
    """Test if the agent module can be imported"""
    print("\n🧪 Testing agent import...")
    
    try:
        # Add current directory to path
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        
        # Try to import the main classes
        from qwen_agent import QwenCoderAgent, Colors, FileChange
        print("✅ QwenCoderAgent imported successfully")
        return True
    except ImportError as e:
        print(f"❌ Failed to import QwenCoderAgent: {e}")
        return False
    except Exception as e:
        print(f"❌ Error importing agent: {e}")
        return False

def test_config_creation():
    """Test configuration file creation"""
    print("\n🧪 Testing configuration...")
    
    try:
        # Create temporary config
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            config = {
                "api_key": "test_key",
                "model": "test_model",
                "max_tokens": 1000
            }
            json.dump(config, f)
            temp_config = f.name
        
        # Try to load it
        with open(temp_config, 'r') as f:
            loaded_config = json.load(f)
        
        assert loaded_config["api_key"] == "test_key"
        print("✅ Configuration creation and loading")
        
        # Cleanup
        os.unlink(temp_config)
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False

def test_file_operations():
    """Test basic file operations"""
    print("\n🧪 Testing file operations...")
    
    try:
        # Create temporary directory
        with tempfile.TemporaryDirectory() as temp_dir:
            test_file = os.path.join(temp_dir, "test.py")
            
            # Test file creation
            with open(test_file, 'w') as f:
                f.write("print('Hello, World!')")
            
            # Test file reading
            with open(test_file, 'r') as f:
                content = f.read()
            
            assert "Hello, World!" in content
            print("✅ File creation and reading")
            
            # Test file modification
            with open(test_file, 'w') as f:
                f.write("print('Modified!')")
            
            with open(test_file, 'r') as f:
                new_content = f.read()
            
            assert "Modified!" in new_content
            print("✅ File modification")
            
        return True
        
    except Exception as e:
        print(f"❌ File operations test failed: {e}")
        return False

def test_agent_initialization():
    """Test agent initialization without API calls"""
    print("\n🧪 Testing agent initialization...")
    
    try:
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        from qwen_agent import QwenCoderAgent
        
        # Create temporary config
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            config = {
                "api_key": "",
                "model": "test_model",
                "max_tokens": 1000,
                "auto_apply_changes": False
            }
            json.dump(config, f)
            temp_config = f.name
        
        # Initialize agent
        agent = QwenCoderAgent(temp_config)
        
        assert agent.config["model"] == "test_model"
        assert agent.config["max_tokens"] == 1000
        print("✅ Agent initialization")
        
        # Test basic methods
        context = agent.get_repository_context(include_file_contents=False)
        assert isinstance(context, str)
        print("✅ Repository context generation")
        
        # Cleanup
        os.unlink(temp_config)
        return True
        
    except Exception as e:
        print(f"❌ Agent initialization test failed: {e}")
        return False

def test_api_key_detection():
    """Test API key detection from environment"""
    print("\n🧪 Testing API key detection...")
    
    try:
        # Test with environment variable
        original_token = os.environ.get("HF_TOKEN")
        os.environ["HF_TOKEN"] = "test_token_123"
        
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        from qwen_agent import QwenCoderAgent
        
        agent = QwenCoderAgent()
        assert agent.config["api_key"] == "test_token_123"
        print("✅ Environment variable detection")
        
        # Restore original token
        if original_token:
            os.environ["HF_TOKEN"] = original_token
        else:
            del os.environ["HF_TOKEN"]
        
        return True
        
    except Exception as e:
        print(f"❌ API key detection test failed: {e}")
        return False

def test_safety_checks():
    """Test command safety checks"""
    print("\n🧪 Testing safety checks...")
    
    try:
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        from qwen_agent import QwenCoderAgent
        
        agent = QwenCoderAgent()
        
        # Test safe commands
        assert agent.is_safe_command("ls -la") == True
        assert agent.is_safe_command("python test.py") == True
        assert agent.is_safe_command("git status") == True
        print("✅ Safe command detection")
        
        # Test dangerous commands
        assert agent.is_safe_command("rm -rf /") == False
        assert agent.is_safe_command("sudo rm important_file") == False
        assert agent.is_safe_command("dd if=/dev/zero") == False
        print("✅ Dangerous command detection")
        
        return True
        
    except Exception as e:
        print(f"❌ Safety checks test failed: {e}")
        return False

def run_all_tests():
    """Run all tests"""
    print("🧪 Qwen Coder Agent - Test Suite")
    print("=" * 50)
    
    tests = [
        ("Import Tests", test_imports),
        ("Agent Import", test_agent_import),
        ("Configuration", test_config_creation),
        ("File Operations", test_file_operations),
        ("Agent Initialization", test_agent_initialization),
        ("API Key Detection", test_api_key_detection),
        ("Safety Checks", test_safety_checks)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} ERROR: {e}")
    
    print(f"\n{'='*50}")
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The agent is ready to use.")
        print("\n🚀 Next steps:")
        print("1. Set your HF_TOKEN: export HF_TOKEN='your_token_here'")
        print("2. Run the agent: python3 qwen-agent.py")
        print("3. Try: qwen-agent> help")
    else:
        print("⚠️  Some tests failed. Check the errors above.")
        print("Make sure all dependencies are installed:")
        print("pip install -r requirements.txt")
    
    return passed == total

def main():
    """Main test function"""
    if len(sys.argv) > 1 and sys.argv[1] == "--quick":
        # Quick test - just imports and basic functionality
        print("🚀 Quick Test Mode")
        success = test_imports() and test_agent_import()
        if success:
            print("✅ Quick test passed!")
        else:
            print("❌ Quick test failed!")
        return success
    else:
        # Full test suite
        return run_all_tests()

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
